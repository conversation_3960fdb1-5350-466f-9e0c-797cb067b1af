function [data,lat,lon,info] = tifBriefExtract(tifName)
% 简单提取tif中的高程以及经纬样点，即提供最简信息
coder.extrinsic('readgeoraster');
% 解析tiff，data为高程数据，info为区域信息
[data, info] = readgeoraster(tifName);
% 获取投影坐标范围
xLimits = info.XWorldLimits; % 投影坐标 X 范围
yLimits = info.YWorldLimits; % 投影坐标 Y 范围

% 构建投影坐标系对象
projCRS = info.ProjectedCRS;   % 读取tiff数据中的投影坐标系
disp(projCRS);

% 生成网格点（投影坐标系下的 X 和 Y 坐标）
[X, Y] = meshgrid(linspace(xLimits(1), xLimits(2), info.RasterSize(2)), ...
                  linspace(yLimits(1), yLimits(2), info.RasterSize(1)));

% 将投影坐标转换为地理坐标（经纬度）
[lat, lon] = projinv(projCRS, X, Y);
lat=lat(:,1);
lon=lon(1,:);
data=double(flipud(data));
end