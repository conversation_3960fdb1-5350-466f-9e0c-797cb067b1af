from flask import Blueprint, request, jsonify
from flask_jwt_extended import  jwt_required
from pymongo import MongoClient
from collections import OrderedDict         # ✅ 用于保持顺序
import my_code.radar_code as radar_code

scene_parameter = Blueprint("scene_parameter", __name__)
client = MongoClient('mongodb://localhost:27017/')
db_base_data=client['base_data']
db_base_data_radar=db_base_data['radar']


#查询成像参数
@scene_parameter.route('/check_scene_parameters', methods=['POST'])
@jwt_required()
def web_check_scene_parameter():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        db_this_radar=client[data["radar_ID"]]
        result = radar_code.ArcSAR_id_map[data["radar_ID"]].query_scene_parameter()
        # result = radar_code.query_scene_parameter(data["radar_ID"])
        db_scene_parameter=db_this_radar["scene_parameter"]
        result_doc=db_scene_parameter.find({"serial_number":{"$gte":1,"$lte":24}})
        scene_parameter_data={}
        for each_doc in result_doc:
            scene_parameter_data[each_doc['code_name']]=str(each_doc['data'])
        if result=="success":
            return jsonify({
                "states": "success",  # 状态信息
                "message": "查询成功",  # 附加消息
                "data": scene_parameter_data  # 数据
            })
        elif result=="error":
            return jsonify({"states": "warning", "message": "查询失败，数据为雷达最后在线时的算法参数", "data": scene_parameter_data})
        else:
            return jsonify({"states": "error", "message": result, "data": scene_parameter_data})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"states": "error", "message": str(e)}), 500



#更新成像参数
@scene_parameter.route('/update_scene_parameter', methods=['POST'])
@jwt_required()
def web_update_scene_parameter():
    try:
        data = OrderedDict(request.get_json())  # 获取前端提交的 JSON 数据
        db_this_radar=client[data["radar_ID"]]
        db_scene_parameter=db_this_radar["scene_parameter"]
        for key, value in data.items():
            if value=="":
                continue
            #读取最后一个字符
            if (key == "radar_ID"):
                break
            doc_scene_parameter=db_scene_parameter.find_one({"code_name":key})
            if doc_scene_parameter["data_type"][:3]=="int":
                value=int(value)
            elif doc_scene_parameter["data_type"][:3]=="flo":
                value=float(value)
            db_scene_parameter.update_one({"code_name":key},{"$set":{"data":value}})
        result = radar_code.ArcSAR_id_map[data["radar_ID"]].set_scene_parameter_to_radar()
        # result = radar_code.set_scene_parameter_to_radar(data["radar_ID"])
        if result=="success":
            return jsonify({"states": "success", "message": "雷达成像参数设置成功"})
        elif result=="error":
            return jsonify({"states": "error", "message": "雷达成像参数设置失败"})
        else:
            return jsonify({"states": "warning", "message": result})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"states": "error", "message": str(e)})
