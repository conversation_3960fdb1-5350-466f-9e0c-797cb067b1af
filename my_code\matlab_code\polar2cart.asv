function [result] = polar2cart(ImgPath, savePath, DEMPath, PolarPara, radarPara, ResampleFlag)
    %ImgPath:原始形变文件地址
    %savePath:笛卡尔转换后图像地址
    %DEMPath:DEM文件地址
    %PolarPara:图像参数[起始角度、每像素角度间隔、起始半径、每像素的半径间隔、雷达朝向]
    %radarPara:[雷达经度，雷达维度]
    %ImgRange:[经度起始，经度结束，维度起始，维度结束]
    %ResampleFlag:是否重采样标志
    
    %% 读取图像
    t = Tiff(ImgPath, 'r');
    img = rot90(double(read(t)));                   % 确保数据为 double
    % img=abs(double(rort90(load(ImgPath).ScaImg)));
    img = 20 * log10(img / max(img(:)));            % dB 归一化
    [rows, cols] = size(img);
    %读取参数
    info = imfinfo(ImgPath);
    metadata = jsondecode(info.ImageDescription);
    theta0 = metadata.theta0;
    dtheta = metadata.dtheta;
    r0 = metadata.r0;
    dr = metadata.dr;
    


    theta0 = PolarPara(1);
    % theta0 = PolarPara(1)-PolarPara(5);
    dTheta = PolarPara(2);                          % 每像素角度间隔（弧度）
    r0 = PolarPara(3);                              % 起始半径（单位：m）
    dr = PolarPara(4);                              % 每像素的半径间隔（单位：m）


    %获取图像范围
    radarLon = radarPara(1);
    radarLat = radarPara(2);
    theta = pi/2-(theta0 + (0:cols-1) * dTheta);        % 角度数组
    r = flip(r0 + (0:rows-1) * dr);                     % 半径数组（真实物理单位）
    [Theta, R] = meshgrid(theta, r);                    % 生成极坐标网格
    
    % 极坐标 → 笛卡尔坐标
    [X, Y] = pol2cart(Theta, R);
    
    % 生成合适的笛卡尔坐标网格
    x_min = min(X(:)); x_max = max(X(:));
    y_min = min(Y(:)); y_max = max(Y(:));
    
    %计算图片经纬度
    % 椭球模型（WGS84，单位米）
    ellipsoid = wgs84Ellipsoid('meters');
    
    [~, lon_begin] = reckon(radarLat, radarLon, x_min, 90, ellipsoid);    %西边
    [~, lon_end] = reckon(radarLat, radarLon, x_max, 90, ellipsoid);      %东边
    [lat_begin, ~] = reckon(radarLat, radarLon, y_min, 0, ellipsoid);     %南边
    [lat_end, ~] = reckon(radarLat, radarLon, y_max, 0, ellipsoid);       %北边
    
    ImgRange=[lon_begin,lon_end,lat_begin,lat_end];

    %生成极坐标网络
    % theta = pi-(theta0 + (0:cols-1) * dTheta);    % 角度数组
    theta = (theta0 + (0:cols-1) * dTheta);         % 角度数组
    r = flip(r0 + (0:rows-1) * dr);                 % 半径数组（真实物理单位）
    [Theta, R] = meshgrid(theta, r);                % 生成极坐标网格

    %% 读取DEM
    %DEM切块
    [DEM_data,lat,lon,info] = tifBriefExtract(DEMPath);
    DEM_data(DEM_data < -300) = -300;               %DEM数据小于-300米显示-300
    indices_lat = find(lat >= ImgRange(3) & lat <= ImgRange(4));
    % lat = lat(indices_lat(1)-1:indices_lat(end)+1);%扩大范围
    lat = lat(indices_lat(1):indices_lat(end));
    indices_lon = find(lon >= ImgRange(1) & lon <= ImgRange(2));
    % lon = lon(indices_lon(1)-1:indices_lon(end)+1);%扩大范围
    lon = lon(indices_lon(1):indices_lon(end));
    % DEM_data = DEM_data(indices_lat(1)-1:indices_lat(end)+1,indices_lon(1)-1:indices_lon(end)+1);%扩大范围
    DEM_data = DEM_data(indices_lat(1):indices_lat(end),indices_lon(1):indices_lon(end));
    % DEM_data=zeros(size(DEM_data));                 %DEM为0，即转笛卡尔坐标系
    [lon_grid, lat_grid] = meshgrid(lon, lat);
    %DEM分辨率插值
    if ResampleFlag
        d_lat = (ImgRange(4)-ImgRange(3))/rows;
        d_lon = (ImgRange(2)-ImgRange(1))/cols;
        new_lat = lat_grid(1):d_lat:lat_grid(end);
        new_lon = lon_grid(1):d_lon:lon_grid(end);
        [new_lon, new_lat] = meshgrid(new_lon, new_lat);
        
        DEM_data = interp2(lon_grid,lat_grid,DEM_data,new_lon,new_lat,'spline',0);
        lon_grid = new_lon;
        lat_grid = new_lat;
    end
    %% 坐标系转换，计算距离和夹角
    ellipsoid = wgs84Ellipsoid('meter');
    
    
    [x,y,z]=geodetic2ecef(ellipsoid,lat_grid,lon_grid,DEM_data);
    [radar_x,radar_y,radar_z]=geodetic2ecef(ellipsoid,radarPara(2),radarPara(1),radarPara(3));
    Distance = sqrt((x - radar_x).^2 + (y - radar_y).^2 + (z - radar_z).^2);
    az = azimuth(radarPara(2) * ones(size(lat_grid)), radarPara(1) * ones(size(lon_grid)), lat_grid, lon_grid,ellipsoid);

    %% 输入坐标查询距离雷达的距离
    my_lat=23.35925;my_lon=113.06062;
    indices_lat = find(lat >= my_lat);
    indices_lon = find(lon >= my_lon);
    fprintf('距离为:%.2f，角度为:%.2f°', Distance(indices_lat(1),indices_lon(1)),az(indices_lat(1),indices_lon(1)));
    %% 插值
    % Theta(Theta<0)=Theta(Theta<0)+2*pi;
    Theta=2*pi+Theta;
    az(az<rad2deg(PolarPara(1)+2*pi))=az(az<rad2deg(PolarPara(1)+2*pi))+360;
    [in_data] = interp2(rad2deg(Theta),R,img,az,Distance,"nearest",-Inf);
    %图像上下反转，最下面为维度最低点
    figure;
    imagesc(lon,lat,in_data);
    colormap('jet');title("差值后图像");
    %% 保存图像
    save_img=flipud(in_data);
    
    temp_data = in_data(:);
    min_data = temp_data(find(temp_data>min(temp_data),1));
    save_img(save_img <= min_data)=min_data;
    
    alpha = ones(size(save_img));
    min_data=min([min_data,min_val]);
    alpha(save_img <= min_data) = 0;
    
    save_img=save_img-min(save_img(:));
    save_img=round(save_img/max(save_img(:))*255)+1;
    cmap = jet(256);  % 获取256色的jet颜色映射
    rgb_img = uint8(ind2rgb(save_img, cmap)*255);
    imwrite(rgb_img,savePath,'Alpha',alpha);










    % t = Tiff(file, 'r');
    % img = rot90(double(read(t))); % 确保数据为 double
    % lon = radarPara(1);
    % lat = radarPara(2);
    % close(t);
    % 
    % % dB 归一化
    % img = 20 * log10(img / max(img(:)));
    % 
    % % 获取极坐标网格
    % [rows, cols] = size(img);
    % theta0 = PolarPara(1)-PolarPara(5)+pi/2;                % 起始角度（弧度）
    % % theta0 = 0.87266222;                          % 起始角度（弧度）
    % dTheta = PolarPara(2);                             % 每像素角度间隔（弧度）
    % r0 = PolarPara(3);                                       % 起始半径（单位：m）
    % dr = PolarPara(4);                                       % 每像素的半径间隔（单位：m）
    % 
    % theta = pi-(theta0 + (0:cols-1) * dTheta);      % 角度数组
    % r = flip(r0 + (0:rows-1) * dr);                 % 半径数组（真实物理单位）
    % [Theta, R] = meshgrid(theta, r);                % 生成极坐标网格
    % 
    % % 极坐标 → 笛卡尔坐标
    % [X, Y] = pol2cart(Theta, R);
    % 
    % % 生成合适的笛卡尔坐标网格
    % x_min = min(X(:)); x_max = max(X(:));
    % y_min = min(Y(:)); y_max = max(Y(:));
    % 
    % %计算图片经纬度
    % % 椭球模型（WGS84，单位米）
    % ellipsoid = wgs84Ellipsoid('meters');
    % 
    % [~, lon_begin] = reckon(lat, lon, x_min, 90, ellipsoid);
    % [~, lon_end] = reckon(lat, lon, x_max, 90, ellipsoid);
    % [lat_begin, ~] = reckon(lat, lon, y_min, 0, ellipsoid);
    % [lat_end, ~] = reckon(lat, lon, y_max, 0, ellipsoid);
    % 
    % ImgRange=[lon_begin,lon_end,lat_begin,lat_end];
end