from datetime import datetime ,timedelta              #时间戳转换
from bson import ObjectId
from flask import Blueprint, request, jsonify
from flask_jwt_extended import  jwt_required
from pymongo import MongoClient
import my_code.radar_code as radar_code


data_analysis = Blueprint("data_analysis", __name__)
client = MongoClient('mongodb://localhost:27017/')
db_base_data=client['base_data']
db_base_data_radar=db_base_data['radar']
db_base_data_scene=db_base_data['scene']


# 获取雷达场景坐标
@data_analysis.route("/get_scene_coordinates", methods=["POST"])
@jwt_required()
def web_get_scene_coordinates():
    """
    @api {post} /get_scene_coordinates 获取场景坐标
    @apiName GetSceneCoordinates
    @apiGroup DataAnalysis
    @apiPermission jwt_required

    @apiParam {String} scene_ID 场景ID

    @apiSuccess {String} status 请求状态(success/error)
    @apiSuccess {String} message 返回消息
    @apiSuccess {Array} data 场景坐标数据
    @apiError {String} status 错误状态
    @apiError {String} message 错误信息
    """
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        scene_ID = data["scene_ID"]
        db_scene = db_base_data_scene.find_one({"_id": ObjectId(scene_ID)})
        send_data = db_scene["coordinates"]
        if(len(send_data)==0):
            return jsonify({"status": "error", "message": "雷达未设置场景坐标","data":[]})
        return jsonify({"status": "success", "message": "雷达场景坐标加载成功", "data": send_data})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 获取雷达坐标
@data_analysis.route('/get_radar_host', methods=['POST'])
@jwt_required()
def web_get_radar_host():
    try:
        data = request.get_json()
        radar_ID=data['radar_ID']
        db_radar_doc=db_base_data_radar.find_one({"ID":radar_ID})
        send_data = db_radar_doc["radar_coordinates"]
        if(len(send_data)==0):
            return jsonify({"status": "warning", "": "雷达未设置坐标","data":[]}), 500
        return jsonify({"status": "success", "message": "雷达坐标加载成功", "data": send_data})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 获取最新形变图像URL
@data_analysis.route('/get_lastest_Img', methods=['POST'])
@jwt_required()
def web_lastest_Img():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        # print(data)
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        db_this_radar=client[radar_ID]
        db_img_data=db_this_radar["img_data_"+mission_ID]
        base_data_doc = db_img_data.find_one({"任务ID": int(mission_ID)}, sort=[("时间戳", -1)])
        file_path = base_data_doc["road_xy"]
        print("http://127.0.0.1:5000/"+file_path)
        return jsonify({"status": "success","message":"http://127.0.0.1:5000/"+file_path})
        # return jsonify({"status": "error", "message": "获取错误"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 获取最新形变图像URL
@data_analysis.route('/get_history_Img', methods=['POST'])
@jwt_required()
def web_history_Img():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        print(data)
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        begin_time = data['begin_time']
        end_time = data['end_time']
        db_this_radar=client[radar_ID]
        db_img_data=db_this_radar["img_data_"+mission_ID]
        end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M").timestamp() #解析为 datetime 对象,后转为时间戳
        print(end_time)
        base_data_doc = db_img_data.find_one({"任务ID": int(mission_ID),"时间戳":{"$lte": end_time+1}}, sort=[("时间戳", -1)])
        print(base_data_doc)
        file_path = base_data_doc["road_xy"]
        print("http://127.0.0.1:5000/"+file_path)
        return jsonify({"status": "success","message":{"img_url":"http://127.0.0.1:5000/"+file_path}})
        # return jsonify({"status": "error", "message": "获取错误"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 加载任务列表
@data_analysis.route('/check_all_mission', methods=['POST'])
@jwt_required()
def web_check_all_mission():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        mission_list_doc=db_base_data_radar.find_one({"ID":radar_ID},{"mission_ID": 1, "_id": 0})
        mission_list=[]
        if mission_list_doc is None:
            return jsonify({"status": "warning", "message": "雷达未创建任务"})
        for each_mission_ID in mission_list_doc["mission_ID"]:
            mission_list.append({"mission_ID":each_mission_ID["ID"],"name":each_mission_ID["name"]})
        return jsonify({"status": "success", "message": "成功加载任务列表", "data": mission_list})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 加载任务时间段
@data_analysis.route('/check_mission_time', methods=['POST'])
@jwt_required()
def web_check_mission_time():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        base_data_doc=db_base_data_radar.find_one({"ID":radar_ID})
        if base_data_doc:
            if any(mission.get("ID") == mission_ID for mission in base_data_doc["mission_ID"]):
                db_this_radar=client[radar_ID]
                db_img_data=db_this_radar["img_data_"+mission_ID]
                result = list(db_img_data.aggregate([{"$group": {
                    "_id": None,
                    "max_value": {"$max": "$时间戳"},
                    "min_value": {"$min": "$时间戳"}
                    }}
                ]))
                max_time=datetime.fromtimestamp(result[0]["max_value"]).strftime('%Y-%m-%d %H:%M')
                min_time=datetime.fromtimestamp(result[0]["min_value"]).strftime('%Y-%m-%d %H:%M')
                return jsonify({"max_time":max_time, "min_time":min_time})
        return jsonify({"status": "error", "message": "获取错误"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 获取监测区域列表
@data_analysis.route('/check_all_monitor_area', methods=['POST'])
@jwt_required()
def web_check_monitor_area_all():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        db_this_radar=client[radar_ID]
        db_monitor_area=db_this_radar["monitor_area_"+mission_ID]
        all_data = list(db_monitor_area.find())
        send_data=[]
        for each_data in all_data:
            temp_send_data={
                "name":each_data["标签"],
                "description":each_data["描述"],
                "westLon":each_data["westLon"],
                "southLat":each_data["southLat"],
                "eastLon":each_data["eastLon"],
                "northLat":each_data["northLat"],
            }
            send_data.append(temp_send_data)
        return jsonify({"status": "success", "message": "监测点列表加载成功", "data": send_data})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 添加监测区域
@data_analysis.route('/add_monitor_area', methods=['POST'])
@jwt_required()
def web_add_monitor_area():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        db_this_radar=client[radar_ID]
        db_monitor_area=db_this_radar["monitor_area_"+mission_ID]
        data = {
            "标签":data["name"],
            "描述":data["description"],
            "westLon":data["westLon"],
            "southLat":data["southLat"],
            "eastLon":data["eastLon"],
            "northLat":data["northLat"],
        }
        db_monitor_area.insert_one(data)
        return jsonify({"status": "success", "message": "添加成功"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500

# 删除监测区域
@data_analysis.route('/delete_monitor_area', methods=['POST'])
@jwt_required()
def web_delete_monitor_area():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        area_name=data['area_name']
        db_this_radar=client[radar_ID]
        db_monitor_area=db_this_radar["monitor_area_"+mission_ID]
        result = db_monitor_area.delete_one({"标签":area_name})
        if result.deleted_count == 0:
            return jsonify({"status": "success", "message": "删除成功，但未找到对应数据"})
        return jsonify({"status": "success", "message": "删除成功"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 雷达启动或停止
@data_analysis.route('/radar_control', methods=['POST'])
@jwt_required()
def web_radar_control():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        print(data)
        radar_ID=data["radar_ID"]
        is_work=bool(int(data["is_work"]))
        result=radar_code.ArcSAR_id_map[radar_ID].radar_work_control(not is_work)
        if result=="success":
            return jsonify({"status": "success", "message": "雷达设置成功"})
        elif result=="error":
            return jsonify({"status": "error", "message": "雷达设置失败"})
        else:
            return jsonify({"status": "warning", "message": result})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


# 查询监测区域形变值
@data_analysis.route('/get_monitor_area_deformation', methods=['POST'])
@jwt_required()
def web_get_monitor_area_deformation():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        print(data)
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        area_list=data['area_list']
        begin_time = datetime.fromisoformat(data['begin_time'].replace("Z", "+00:00")) 
        end_time = datetime.fromisoformat(data['end_time'].replace("Z", "+00:00")) + timedelta(minutes=1)
        db_this_radar=client[radar_ID]
        coll_deformation=db_this_radar["deformation_data_"+mission_ID]
        deformation_data = coll_deformation.find({"area_name":{"$in":area_list},"timestamp":{"$gte":begin_time,"$lte":end_time}}).sort("timestamp",1)
        send_data = []
        for each_data in deformation_data:
            temp_data = {
                "deformation":each_data["deformation"],
                "timestamp":each_data["timestamp"].strftime('%Y-%m-%d %H:%M:%S'),
                "area_name":each_data["area_name"],
            }
            send_data.append(temp_data)
        return jsonify({"status": "success", "message": "成功获取", "data": send_data})


    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500
