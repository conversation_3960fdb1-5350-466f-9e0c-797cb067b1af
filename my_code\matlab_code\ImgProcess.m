classdef ImgProcess
    methods(Static)
        function x = add(A, B)
            x = A + B;
        end
        function x = sub(A, B)
            x = A - B;
        end
        function x = test(A)
            x=[A(1)+A(2),A(1)-A(2)];
        end
        function ImgRange = Img2DEM(PolarPara,radarPara,ImgFlie,DEMFile,output_path,Resample_flag)
            %PolarPara:图像参数[起始角度、每像素角度间隔、起始半径、每像素的半径间隔、雷达朝向]
            %radarPara:[雷达经度，雷达维度]
            %ImgRange:[经度起始，经度结束，维度起始，维度结束]
            %img:输入的图像数据

            %获取图像坐标
            t = Tiff(ImgFlie, 'r');
            img = rot90(double(read(t)));                   % 确保数据为 double
            img = 20 * log10(img / max(img(:)));            % dB 归一化
            [rows, cols] = size(img);
            theta0 = PolarPara(1)-PolarPara(5);
            dTheta = PolarPara(2);                          % 每像素角度间隔（弧度）
            r0 = PolarPara(3);                              % 起始半径（单位：m）
            dr = PolarPara(4);                              % 每像素的半径间隔（单位：m）
            theta = (theta0 + (0:cols-1) * dTheta);         % 角度数组
            r = flip(r0 + (0:rows-1) * dr);                 % 半径数组（真实物理单位）
            [Theta, R] = meshgrid(theta, r);                % 生成极坐标网格

            %获取图片经纬度范围
            ImgRange=polar2cart(ImgFlie,PolarPara,radarPara);
            % [X, Y] = pol2cart(Theta, R);
            % x_min = min(X(:)); x_max = max(X(:));
            % y_min = min(Y(:)); y_max = max(Y(:));
            % ellipsoid = wgs84Ellipsoid('meter');
            % [~, lon_begin] = reckon(radarPara(2), radarPara(1), x_min, 90, ellipsoid);
            % [~, lon_end] = reckon(radarPara(2), radarPara(1), x_max, 90, ellipsoid);
            % [lat_begin, ~] = reckon(radarPara(2), radarPara(1), y_min, 0, ellipsoid);
            % [lat_end, ~] = reckon(radarPara(2), radarPara(1), y_max, 0, ellipsoid);
            % ImgRange=[lon_begin,lon_end,lat_begin,lat_end];
            %% 读取DEM
            %DEM切块
            [DEM_data,lat,lon,info] = tifBriefExtract(DEMFile);
            indices_lat = find(lat >= ImgRange(3) & lat <= ImgRange(4));
            lat = lat(indices_lat(1)-1:indices_lat(end)+1);
            indices_lon = find(lon >= ImgRange(1) & lon <= ImgRange(2));
            lon = lon(indices_lon(1)-1:indices_lon(end)+1);
            DEM_data = DEM_data(indices_lat(1)-1:indices_lat(end)+1,indices_lon(1)-1:indices_lon(end)+1);
            [lon, lat] = meshgrid(lon, lat);
            %DEM分辨率插值
            if Resample_flag
                d_lat = (ImgRange(4)-ImgRange(3))/rows;
                d_lon = (ImgRange(2)-ImgRange(1))/cols;
                new_lat = lat(1):d_lat:lat(end);
                new_lon = lon(1):d_lon:lon(end);
                [new_lon, new_lat] = meshgrid(new_lon, new_lat);
                
                DEM_data = interp2(lon,lat,DEM_data,new_lon,new_lat,'spline',0);
                lon = new_lon;
                lat = new_lat;
            end
            %坐标系转换，计算距离和夹角
            ellipsoid = wgs84Ellipsoid('meter');
            
            
            [x,y,z]=geodetic2ecef(ellipsoid,lat,lon,DEM_data);
            [radar_x,radar_y,radar_z]=geodetic2ecef(ellipsoid,radarPara(2),radarPara(1),radarPara(3));
            Distance = sqrt((x - radar_x).^2 + (y - radar_y).^2 + (z - radar_z).^2);
            
            az = azimuth(radarPara(2) * ones(size(lat)), radarPara(1) * ones(size(lon)), lat, lon,ellipsoid);
            
            %插值
            [in_data] = interp2(rad2deg(Theta),R,img,az,Distance,"nearest",-Inf);
            %图像上下反转，最下面为维度最低点
            % imagesc(flipud(in_data));
            % in_data = flipud(in_data);
            %% 保存图像用
            img_cart = in_data;
            % 确保没有坐标轴
            fig = figure('Visible', 'off');  % 不弹出图窗;
            h = imagesc(lon(1,:), lat(:,1), img_cart);
            colormap(jet);
            axis off;
            axis equal;  % 保持纵横比
            set(gca, 'YDir', 'normal');
            
            % 计算 Alpha 通道（最小值透明）
            min_val = min(img_cart(:));
            alphaData = img_cart > min_val;
            set(h, 'AlphaData', alphaData);
            
            % 设置图窗位置和大小以匹配图像尺寸
            [rows, cols] = size(img_cart);
            set(fig, 'Units', 'pixels', 'Position', [100, 100, cols, rows]);  % 位置[x,y,width,height]
            
            % 确保坐标轴填充整个图窗
            set(gca, 'Position', [0 0 1 1]);  % [left, bottom, width, height]
            
            % 使用 export_fig 生成透明PNG（保持原始尺寸）
            export_fig(output_path, '-png', '-transparent', '-m1');  % -m1表示1倍大小

            %% 返回图像范围结果
            ImgRange = [lon(1,1),lat(1,1),lon(end,end),lat(end,end)];
        end
    end
end
