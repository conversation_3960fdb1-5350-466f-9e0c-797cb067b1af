# 项目结构说明

## 目录结构

```
.
├── app.py                         # Flask应用程序入口文件
└── my_code/                       # 主要代码目录
    ├── __init__.py               # Python包初始化文件
    ├── radar_code.py             # 雷达核心功能实现
    ├── matlab_test.py            # MATLAB测试相关功能
    ├── algorithm_code/           # 算法相关代码目录
    │   ├── __init__.py          # 算法包初始化文件
    │   └── read_img.py          # 图像读取处理模块
    ├── matlab_code/              # MATLAB代码目录
    │   ├── deformation.m        # 形变计算MATLAB脚本
    │   ├── ImgProcess.m         # 图像处理MATLAB脚本
    │   ├── polar2cart.m         # 极坐标转笛卡尔坐标MATLAB脚本
    │   └── saveAsTiffSingle.m   # TIFF文件保存MATLAB脚本
    └── web_code/                 # Web应用相关代码目录
        ├── data_analysis.py      # 数据分析模块
        ├── radar_information.py  # 雷达信息处理模块
        ├── radar_manage.py       # 雷达管理模块
        ├── scene_parameter.py    # 场景参数处理模块
        └── user.py              # 用户认证和管理模块
```
## 主要模块说明

### 1. Web应用核心模块

#### app.py
- 作为Flask应用程序的入口点
- 配置和初始化Flask应用
- 注册所有蓝图和路由

#### my_code/web_code/

##### data_analysis.py
- 处理数据分析相关的API接口
- 提供以下功能：
  * 获取场景坐标信息
  * 获取雷达坐标信息
  * 获取最新和历史形变图像
  * 管理监测区域
  * 控制雷达工作状态
  * 查询监测区域形变值

##### radar_manage.py
- 处理雷达管理相关的API接口
- 提供以下功能：
  * 获取所有雷达列表
  * 雷达和任务重命名
  * 删除雷达和任务
  * 场景和雷达坐标设置

##### radar_information.py
- 处理雷达信息相关的功能
- 管理雷达基本信息和配置

##### scene_parameter.py
- 处理场景参数相关的功能
- 管理场景配置和参数设置

##### user.py
- 处理用户认证和授权
- 管理用户账户和权限

### 2. 算法和处理模块

#### my_code/algorithm_code/
- read_img.py: 处理图像读取和预处理功能

#### my_code/matlab_code/
- 包含多个MATLAB脚本文件，用于：
  * 形变计算（deformation.m）
  * 图像处理（ImgProcess.m）
  * 坐标转换（polar2cart.m）
  * 文件保存（saveAsTiffSingle.m）

#### my_code/radar_code.py
- 实现雷达核心功能
- 处理雷达数据采集和控制

## 模块关系图

```mermaid
graph TB
    A[app.py] --> B[web_code]
    A --> C[radar_code.py]
    
    subgraph Web模块
    B --> D[data_analysis.py]
    B --> E[radar_manage.py]
    B --> F[radar_information.py]
    B --> G[scene_parameter.py]
    B --> H[user.py]
    end
    
    subgraph 算法模块
    I[algorithm_code] --> J[read_img.py]
    end
    
    subgraph MATLAB模块
    K[matlab_code] --> L[deformation.m]
    K --> M[ImgProcess.m]
    K --> N[polar2cart.m]
    K --> O[saveAsTiffSingle.m]
    end
    
    C --> I
    C --> K
    D --> C
    E --> C
    F --> C
```

## 数据流说明

1. 前端请求通过app.py路由到相应的web_code模块处理
2. web_code模块调用radar_code.py进行雷达控制和数据处理
3. radar_code.py根据需要调用：
   - algorithm_code中的图像处理功能
   - matlab_code中的数据处理和计算功能
4. 处理结果通过web_code模块返回给前端

## 主要功能流程

1. 用户认证流程
   - 通过user.py处理用户登录和认证
   - 使用JWT进行身份验证

2. 雷达管理流程
   - 通过radar_manage.py管理雷达设备
   - 处理雷达配置和任务管理

3. 数据分析流程
   - 通过data_analysis.py处理数据分析请求
   - 获取形变数据和监测区域信息

4. 场景管理流程
   - 通过scene_parameter.py管理场景参数
   - 处理坐标系统和场景配置

## 数据库结构

项目使用MongoDB数据库，主要包含以下集合：
- base_data.radar: 存储雷达基本信息
- base_data.scene: 存储场景信息
- [radar_ID].img_data_[mission_ID]: 存储图像数据
- [radar_ID].confidence_data_[mission_ID]: 存储置信度数据
- [radar_ID].deformation_data_[mission_ID]: 存储形变数据
- [radar_ID].monitor_area_[mission_ID]: 存储监测区域信息