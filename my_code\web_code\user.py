from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import check_password_hash
from pymongo import MongoClient
import bcrypt                               #哈希加密
import json


user = Blueprint("user", __name__)
client = MongoClient('mongodb://localhost:27017/')
db_base_data=client['base_data']
db_base_data_users=db_base_data['users']





@user.route('/login', methods=['POST'])
def web_login():
    access_token = create_access_token(identity=json.dumps({
        "username": "24216131",
        "role": "管理员",
    }))
    return jsonify(access_token=access_token, username="admin", role="管理员")

    data = request.json
    username = data.get('username')
    password = data.get('password')
    # 查询数据库，获取符合条件的文档
    user_data = db_base_data_users.find_one({'username': username})
    if not user_data:
        return jsonify({"status": "error", "message": "用户不存在"}), 400

    elif not bcrypt.checkpw(password.encode(), user_data['password'].encode()):
        return jsonify({"status": "error", "message": "密码错误"}), 400
    access_token = create_access_token(identity=json.dumps({
        "username": user_data["username"],
        "role": user_data["role"],
    }))
    return jsonify(access_token=access_token, username=user_data["username"], role=user_data["role"])


@user.route('/register', methods=['POST'])
def web_register():
    data = request.json
    username = data.get('username')
    password = data.get('password')
    salt = bcrypt.gensalt()
    password_hashed = bcrypt.hashpw(password.encode(), salt).decode()  # 进行哈希
    role = "用户"
    
    # 检查用户是否已经存在
    if db_base_data_users.find_one({'username': username}):
        return jsonify({"status": "error", "message": "用户已注册"}), 500
    else:
        db_base_data_users.insert_one({'username': username, 'password': password_hashed, 'role': role})
        access_token = create_access_token(identity=json.dumps({
            "username": username,
            "role": "用户",
        }))
        return jsonify(access_token=access_token, username=username, role=role)
    

@user.route('/change_password', methods=['POST'])
@jwt_required()
def web_change_password():
    data = request.json
    username = data.get('username')
    password = data.get('password')
    salt = bcrypt.gensalt()
    password_hashed = bcrypt.hashpw(password.encode(), salt).decode()  # 进行哈希
    db_base_data_users.update_one({'username': username}, {'$set': {'password': password_hashed}})
    return jsonify({"status": "success", "message": "密码修改成功"}), 200