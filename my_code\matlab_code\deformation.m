function [result] = deformation(Img1Path, Img2Path, outputPath)
    %% 读取参数(读取tif文件中的参数)
    info = imfinfo(ImgPath);
    metadata = jsondecode(info(1).ImageDescription);
    theta0 = metadata.theta0;
    dTheta = metadata.dtheta;
    r0 = metadata.r0;
    dr = metadata.dr;
    radar_zero_theta = metadata.radar_zero_theta;
    radarPara = metadata.coordinates;
    PolarPara = [theta0,dTheta,r0,dr,radar_zero_theta];
    %% 读取图像
    img1 = tiffreadVolume(Img1Path);
    img2 = tiffreadVolume(Img2Path);
    data_Amp(:,:,1) = abs(img1(:,:,1))/max(max(img1(:,:,1)));
    data_Amp(:,:,2) = abs(img2(:,:,1))/max(max(img2(:,:,1)));
    data_Phase(:,:,1)=img1(:,:,2);
    data_Phase(:,:,2)=img2(:,:,2);

    N_rng=size(img1,2);
    N_azm=size(img1,1);
    data_complex=data_Amp.*exp(1j*data_Phase);  %%%得到复散射矩阵

    %% 形变计算
    phase_InSAR = angle(data_complex(:,:,1).*conj(data_complex(:,:,2)));   %复数共轭相乘算出干涉相位
    phase_InSAR = phase_InSAR.*(phase_InSAR<pi & phase_InSAR>=-pi)+(phase_InSAR-2*pi).*(phase_InSAR>=pi)+(2*pi+phase_InSAR).*(phase_InSAR<-pi);
    % 利用滤波对图像进行处理
    H = fspecial('average',[7,15]);
    % 分别对实部虚部进行空间低通滤波
    real_part = real(exp(1j*phase_InSAR));
    imag_part = imag(exp(1j*phase_InSAR)); 
    real_part_filter = imfilter(real_part,H);
    imag_part_filter = imfilter(imag_part,H);
    phase_InSAR = angle(real_part_filter + 1j*imag_part_filter);
    

end

