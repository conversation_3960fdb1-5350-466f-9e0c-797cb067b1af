"""
雷达通信和数据处理模块

此模块提供了与雷达设备通信、数据接收和处理的核心功能。主要功能包括：
- 雷达连接管理
- 数据接收和解析
- 图像数据处理
- 数据库操作
- 文件系统操作

作者: [作者名]
日期: 2024-06-04
"""

from typing import Dict, List, Optional, Tuple, Union, Any
import struct
import time
import socket
import select
import os
import hashlib
import numpy as np
from tifffile import imwrite, imread
import snappy
import multiprocessing
from matplotlib import pyplot as plt
from scipy.interpolate import griddata
import pandas as pd
from scipy.ndimage import rotate
from datetime import datetime
from collections import namedtuple
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection

# 常量定义
BASE_FILE_PATH = ""  # 文件基础存储位置，以/结尾
MONGODB_URI = 'mongodb://localhost:27017/'
HOST = '*************'
PORT = 1030

# MongoDB连接初始化
client: MongoClient = MongoClient(MONGODB_URI)
db_base_data: Database = client['base_data']
db_base_data_radar: Collection = db_base_data['radar']
# 全局变量
stop_server: bool = False  # 控制服务器停止标志
send_sign: bool = False    # 发送信号标志
polar2cart_list: List[Dict[str, Any]] = []  # 坐标转换任务列表

# 通信协议常量
HEAD_HEX: str = "5a5a"     # 平台发送指令头部
RADAR_HEAD_HEX: str = "3c3c"  # 雷达发送指令头部
con_int: int = 0  # 通信序号计数器

# 雷达结构体定义
Radar = namedtuple('Radar', ['ID_str', 'Socket'])
radar_list: List[Radar] = []

# 存储类的字典
ArcSAR_map: Dict[socket.socket, 'ArcSAR'] = {}  # 套接字到ArcSAR实例的映射
ArcSAR_id_map: Dict[str, 'ArcSAR'] = {}  # 雷达ID到ArcSAR实例的映射

class ArcSAR:
    """
    ArcSAR类：管理与单个雷达设备的通信和数据处理
    
    此类封装了与雷达设备通信的所有功能，包括：
    - 雷达状态查询和控制
    - 数据接收和解析
    - 图像数据处理
    - 数据库操作
    """
    
    def __init__(self, sock: socket.socket):
        """
        初始化ArcSAR实例
        
        Args:
            sock: 与雷达设备建立的套接字连接
        """
        self.ID_str: str = ""  # 雷达ID，初始为空，在首次通信时设置
        self.Socket: socket.socket = sock  # 与雷达通信的套接字
        self.receive_len: int = 10240000  # 接收缓冲区大小
        self.head_hex: str = HEAD_HEX  # 平台发送指令头部
        self.radar_head_hex: str = RADAR_HEAD_HEX  # 雷达发送指令头部
        self.receive_data: bytes = b''  # 接收的数据缓冲
        self.con_int: int = 0  # 通信序号计数器
        sock.settimeout(5)  # 设置套接字超时为5秒

    
    def radar_report(self) -> bytes:
        """
        解析雷达上报的指令并处理
        
        此方法接收雷达发送的数据，解析指令头部，并根据扩展码调用相应的处理函数。
        处理函数包括：
        - 00: 更新雷达基本信息
        - 01: 上传图像数据
        - 02/03: 上传形变或置信度数据
        - 04: 上传移动目标数据
        - 05: 添加日志文件
        - 06: 更新雷达时间
        
        Returns:
            bytes: 处理结果，成功返回b'success'，失败返回b''
        """
        data_byte: bytes = self.Socket.recv(self.receive_len)
        if not data_byte:
            return b''
            
        # 定义扩展码对应的处理函数映射
        extend_str_map: Dict[str, callable] = {
            "00": self.update_radar_imformation,
            "01": self.upload_image_data_form_radar,
            "02": self.upload_deformation_or_confidence_data_form_radar,
            "03": self.upload_deformation_or_confidence_data_form_radar,
            "04": self.upload_move_target_data_form_radar,
            "05": self.add_log_form_radar,
            "06": self.update_radar_time
        }
        
        # 解析数据头部
        head_str: str = data_byte[0:2][::-1].hex()
        ID_str: str = data_byte[2:6][::-1].hex()
        command_str: str = data_byte[6:7][::-1].hex()
        extend_str: str = data_byte[7:8][::-1].hex()
        sign_str: str = data_byte[8:9][::-1].hex()
        con_int: int = int(data_byte[9:13][::-1].hex(), 16)
        data_len_int: int = int(data_byte[24:28][::-1].hex(), 16)
        receive_data_byte: bytes = data_byte[28:28+data_len_int]

        # 判断是否雷达第一次连接服务器
        if self.ID_str == "":
            self.ID_str = ID_str
            ArcSAR_id_map[self.ID_str] = self
            self.first_conn()

        print(f"接收到雷达上报指令 {head_str}, 雷达ID: {ID_str}, 命令: {command_str}, 扩展码: {extend_str}")

        # 接收完整数据（针对大数据包）
        if int(extend_str) >= 1 and int(extend_str) <= 5 and int(command_str) == 40:
            print(f"接收到数据,当前字节数为：{len(receive_data_byte)}")
            while len(receive_data_byte) < data_len_int:
                chunk = self.Socket.recv(self.receive_len)
                receive_data_byte += chunk
                print(f"接收到数据,当前字节数为：{len(receive_data_byte)}")
        
        # 更新类内的接收数据
        self.receive_data = receive_data_byte

        # 判断是否为雷达主动上报的指令并处理
        if self.radar_head_hex == head_str and command_str == "40" and (extend_str in extend_str_map):
            # 调用对应的处理函数
            return_data_str: str = extend_str_map[extend_str]()
            data_len_int: int = int(len(return_data_str)/2)
            data_len_str: str = struct.pack('<i', data_len_int).hex()
            
            # 构建响应数据
            send_data_str: str = HEAD_HEX + data_byte[2:8].hex() + "01" + data_byte[9:24].hex() + data_len_str
            if data_len_int != 0:
                send_data_str = send_data_str + return_data_str
                
            # 发送响应
            self.Socket.sendall(bytes.fromhex(send_data_str))
        else:
            print("非雷达主动上报的指令或扩展码错误")
        
        return b'success'
        

    def first_conn(self) -> str:
        """
        处理雷达首次连接的初始化工作
        
        此方法执行以下操作：
        1. 检查雷达是否已在数据库中注册
        2. 如果是新雷达：
           - 在数据库中创建雷达基本信息
           - 创建必要的文件目录结构
           - 从Excel导入雷达配置信息到MongoDB
        3. 如果是已知雷达：
           - 更新在线状态
        
        Returns:
            str: 'successfully!' 表示初始化成功
        
        Raises:
            OSError: 创建目录失败时抛出
            pd.errors.EmptyDataError: Excel文件为空时抛出
            pymongo.errors.PyMongoError: MongoDB操作失败时抛出
        """
        try:
            # 查找是否曾经连接过雷达
            base_radar_data = db_base_data_radar.find_one({"ID": self.ID_str})
            
            if base_radar_data is None:
                print(f"新雷达连接, ID：{self.ID_str}")
                
                # 创建雷达基本信息
                db_base_data_radar.insert_one({
                    "ID": self.ID_str,
                    "name": f"雷达{self.ID_str}",
                    "is_online": 1,
                    "is_work": 0,
                    "mission_ID": [],
                    "radar_coordinates": [],
                    "scene": None,
                })
                
                # 创建文件目录结构
                radar_file_path = os.path.join(BASE_FILE_PATH, self.ID_str)
                for subdir in ["algorithm_file", "log_file", "work_data"]:
                    os.makedirs(os.path.join(radar_file_path, subdir), exist_ok=True)
                print("雷达文件夹创建成功")
                
                # 初始化雷达数据库
                db_radar: Database = client[self.ID_str]
                
                # 从Excel导入配置数据
                excel_sheets = {
                    '雷达信息': 'radar_information',
                    '场景参数': 'scene_parameter',
                    '平台命令': 'platform_command'
                }
                
                for sheet_name, collection_name in excel_sheets.items():
                    df = pd.read_excel('code/data.xlsx', sheet_name=sheet_name)
                    data = df.to_dict(orient='records')
                    db_radar[collection_name].insert_many(data)
                
                print("数据已成功导入到 MongoDB!")
                return 'successfully!'
            else:
                # 更新已存在雷达的在线状态
                db_base_data_radar.update_one(
                    {'ID': self.ID_str}, 
                    {'$set': {'is_online': 1}}
                )
                print(f"雷达上线, ID：{self.ID_str}")
                return 'successfully!'
                
        except OSError as e:
            print(f"创建目录失败: {str(e)}")
            raise
        except pd.errors.EmptyDataError as e:
            print(f"Excel文件读取失败: {str(e)}")
            raise
        except Exception as e:
            print(f"初始化失败: {str(e)}")
            raise



    def update_radar_imformation(self) -> str:
        """
        更新雷达基本信息
        
        此方法从接收到的数据中解析雷达信息并更新到数据库。支持的数据类型包括：
        - charchar: 字符数组，每个字符用点号分隔
        - char: 单个字符
        - float32: 32位浮点数
        - float64: 64位浮点数
        - int16: 16位整数
        - int32: 32位整数
        
        Returns:
            str: 空字符串表示成功
            
        Raises:
            struct.error: 数据解包错误时抛出
            pymongo.errors.PyMongoError: 数据库操作错误时抛出
        """
        try:
            db_this_radar: Database = client[self.ID_str]
            db_radar_information: Collection = db_this_radar["radar_information"]
            
            # 数据类型到解析函数的映射
            type_parsers = {
                'charchar': self._parse_charchar,
                'char': self._parse_char,
                'float32': lambda pos, size: struct.unpack('<f', self.receive_data[pos:pos+size])[0],
                'float64': lambda pos, size: struct.unpack('<d', self.receive_data[pos:pos+size])[0],
                'int16': lambda pos, size: struct.unpack('<h', self.receive_data[pos:pos+size])[0],
                'int32': lambda pos, size: struct.unpack('<I', self.receive_data[pos:pos+size])[0]
            }
            
            # 处理每个雷达信息文档
            for doc in db_radar_information.find():
                start_pos: int = doc.get('start_byte_position')
                byte_size: int = doc.get('all_byte')
                data_type: str = doc.get('data_type')
                
                if data_type not in type_parsers:
                    print(f"警告：未知的数据类型 {data_type}")
                    continue
                    
                try:
                    # 解析数据
                    parsed_data = type_parsers[data_type](start_pos, byte_size)
                    
                    # 更新数据库
                    db_radar_information.update_one(
                        {'start_byte_position': start_pos},
                        {'$set': {'data': parsed_data}}
                    )
                except struct.error as e:
                    print(f"数据解析错误 at position {start_pos}: {str(e)}")
                    continue
                    
            print(f"雷达信息写入完成, ID：{self.ID_str}")
            return ""
            
        except Exception as e:
            print(f"更新雷达信息失败: {str(e)}")
            raise
            
    def _parse_charchar(self, start_pos: int, size: int) -> str:
        """解析字符数组数据"""
        values = []
        values.append('v')  # 添加前缀
        
        for i in range(size):
            byte_val = int(self.receive_data[start_pos + i:start_pos + i + 1].hex())
            values.append(str(byte_val))
            
        return '.'.join(values)
        
    def _parse_char(self, start_pos: int, size: int) -> str:
        """解析单个字符数据"""
        return str(int(self.receive_data[start_pos:start_pos + size][::-1].hex()))
    
    def upload_image_data_form_radar(self) -> str:
        """
        处理雷达上传的图像数据并保存到数据库和文件系统
        
        此方法执行以下操作：
        1. 解析接收到的二进制数据，包括序列号、MD5校验、时间戳、任务ID等元数据
        2. 对图像数据进行MD5校验
        3. 解压缩图像数据并转换为幅度和相位两个numpy数组
        4. 将图像数据保存为TIFF格式，包含元数据
        5. 将图像信息存入MongoDB数据库
        
        Returns:
            str: 空字符串表示成功
            
        Raises:
            struct.error: 数据解包错误时抛出
            snappy.UncompressError: 数据解压缩错误时抛出
            OSError: 文件写入错误时抛出
            pymongo.errors.PyMongoError: 数据库操作错误时抛出
        """
        try:
            # 解析头部数据
            header_data = self._parse_image_header()
            if not header_data:
                return ""
                
            # 解压缩和处理图像数据
            img_data = self._process_image_data(header_data)
            if not img_data:
                return ""
                
            # 准备文件路径和数据库
            file_paths = self._prepare_file_paths(header_data)
            
            # 保存图像数据
            self._save_image_data(header_data, img_data, file_paths)
            
            # 保存到数据库
            self._save_to_database(header_data, file_paths)
            
            print(f"接收到雷达图像数据, ID: {self.ID_str}")
            print(f"图像大小: {header_data['sca_im_size']}")
            
            return ""
            
        except Exception as e:
            print(f"处理图像数据失败: {str(e)}")
            raise
            
    def _parse_image_header(self) -> Optional[Dict[str, Any]]:
        """解析图像数据头部信息"""
        try:
            seq_int = struct.unpack('<I', self.receive_data_byte[0:4])[0]
            md5_str = self.receive_data_byte[4:20].hex()
            
            # MD5校验
            if hashlib.md5(self.receive_data_byte[20:]).hexdigest() != md5_str:
                print(f"图像数据MD5校验不通过, ID: {self.ID_str}")
                return None
                
            return {
                'seq_int': seq_int,
                'time_stamp': struct.unpack('<I', self.receive_data_byte[20:24])[0],
                'mission_ID': struct.unpack('<I', self.receive_data_byte[24:28])[0],
                'rng_res': struct.unpack('<f', self.receive_data_byte[28:32])[0],
                'rng_num': struct.unpack('<I', self.receive_data_byte[32:36])[0],
                'rng_min': struct.unpack('<f', self.receive_data_byte[36:40])[0],
                'ang_res': struct.unpack('<f', self.receive_data_byte[40:44])[0],
                'ang_num': struct.unpack('<I', self.receive_data_byte[44:48])[0],
                'ang_min': struct.unpack('<f', self.receive_data_byte[48:52])[0],
                'data_type': struct.unpack('<I', self.receive_data_byte[52:56])[0],
                'sca_im_size': struct.unpack('<I', self.receive_data_byte[56:60])[0],
                'img_max_amp': struct.unpack('<f', self.receive_data_byte[60:64])[0]
            }
            
        except struct.error as e:
            print(f"解析头部数据失败: {str(e)}")
            return None
            
    def _process_image_data(self, header: Dict[str, Any]) -> Optional[Dict[str, np.ndarray]]:
        """处理图像数据"""
        try:
            M, N = header['rng_num'], header['ang_num']
            img_data_byte = snappy.uncompress(self.receive_data_byte[64:])
            
            return {
                'magnitude': np.frombuffer(img_data_byte[:2*M*N], dtype=np.float16).reshape(N, M).astype(np.float32),
                'phase': np.frombuffer(img_data_byte[2*M*N:], dtype=np.float16).reshape(N, M).astype(np.float32)
            }
            
        except Exception as e:
            print(f"处理图像数据失败: {str(e)}")
            return None
            
    def _prepare_file_paths(self, header: Dict[str, Any]) -> Dict[str, str]:
        """准备文件路径"""
        mission_id = str(header['mission_ID'])
        seq_int = str(header['seq_int'])
        base_path = os.path.join(BASE_FILE_PATH, self.ID_str, "work_data", mission_id, "image_data")
        
        return {
            'polar': os.path.join(base_path, f"magnitude_{seq_int}.tiff"),
            'cart': os.path.join(base_path, f"cart_{seq_int}.png")
        }
        
    def _save_image_data(self, header: Dict[str, Any], img_data: Dict[str, np.ndarray], 
                        file_paths: Dict[str, str]) -> None:
        """保存图像数据"""
        # 获取雷达参数和坐标
        doc_base_this_radar = db_base_data_radar.find_one({"ID": self.ID_str})
        mission_list = doc_base_this_radar["mission_ID"]
        mission_mess = next((temp for temp in mission_list 
                           if temp["ID"] == str(header['mission_ID'])), None)
                           
        metadata = {
            'description': 'first image is magnitude, second image is phase',
            'theta0': header['ang_min'],
            'dtheta': header['ang_res'],
            'r0': header['rng_min'],
            'dr': header['rng_res'],
            'coordinates': mission_mess["radar_coordinates"],
            'radar_zero_theta': mission_mess["radar_zero_theta"]
        }
        
        # 保存图像
        save_img = np.stack([img_data['magnitude'], img_data['phase']], axis=0)
        imwrite(file_paths['polar'], save_img, metadata=metadata)
        
    def _save_to_database(self, header: Dict[str, Any], file_paths: Dict[str, str]) -> None:
        """保存数据到数据库"""
        db_this_radar: Database = client[self.ID_str]
        db_img_data: Collection = db_this_radar[f"img_data_{header['mission_ID']}"]
        
        img_doc = {
            '扫描序号': header['seq_int'],
            '时间戳': header['time_stamp'],
            '任务ID': header['mission_ID'],
            '距离像素间隔': header['rng_res'],
            '距离像素点数量': header['rng_num'],
            '最小距离': header['rng_min'],
            '角度像素间隔': header['ang_res'],
            '角度像素点数量': header['ang_num'],
            '最小角度': header['ang_min'],
            '数据类型': header['data_type'],
            '最大幅值': header['img_max_amp'],
            'road_polar': file_paths['polar'],
            'road_cart': file_paths['cart']
        }
        
        db_img_data.insert_one(img_doc)

    #更新置信度或形变数据
    def upload_deformation_or_confidence_data_form_radar(self):
        seq_int = struct.unpack('<I', self.receive_data_byte[0:4])[0]
        md5_str = self.receive_data_byte[4:20].hex()
        #MD5校验
        if hashlib.md5(self.receive_data_byte[20:]).hexdigest() == md5_str:
            time_stamp=struct.unpack('<I', self.receive_data_byte[20:24])[0]
            mission_ID=struct.unpack('<I', self.receive_data_byte[24:28])[0]
            rng_res=struct.unpack('<f', self.receive_data_byte[28:32])[0]
            rng_num=struct.unpack('<I', self.receive_data_byte[32:36])[0]
            rng_min=struct.unpack('<f', self.receive_data_byte[36:40])[0]
            ang_res=struct.unpack('<f', self.receive_data_byte[40:44])[0]
            ang_num=struct.unpack('<I', self.receive_data_byte[44:48])[0]
            ang_min=struct.unpack('<f', self.receive_data_byte[48:52])[0]
            data_type=struct.unpack('<I', self.receive_data_byte[52:56])[0]
            defo_im_size=struct.unpack('<I', self.receive_data_byte[56:60])[0]
            M=rng_num
            N=ang_num
            data_byte=snappy.uncompress(self.receive_data_byte[60:])
            data_float16 = np.frombuffer(data_byte, dtype=np.float16).reshape(N, M)
            #定位数据库和文件地址
            db_this_radar=client[self.ID_str]
            if data_type == 20:
                file_road=base_file_path+self.ID_str+"/work_data/"+str(mission_ID)+"/deformation_data/"
                file_name = "deformation_img_" + str(seq_int) + ".tiff"
                db_deformation_data=db_this_radar["deformation_data_"+str(mission_ID)]
            elif data_type == 30:
                file_road=base_file_path+self.ID_str+"/work_data/"+str(mission_ID)+"/Confidence_data/"
                file_name = "Confidence_img_" + str(seq_int) + ".tiff"
                db_confidence_data=db_this_radar["confidence_data_"+str(mission_ID)]
            else:
                return ""
            imwrite(file_road+file_name, data_float16)
            img_doc = {
                '扫描序号': seq_int,
                '时间戳': time_stamp,
                '任务ID': mission_ID,
                '距离像素间隔': rng_res,
                '距离像素点数量': rng_num,
                '最小距离': rng_min,
                '角度像素间隔': ang_res,
                '角度像素点数量': ang_num,
                '最小角度': ang_min,
                '数据类型': data_type,
                '图像大小': defo_im_size,
                'road': file_road+file_name
            }
            if data_type == 20:
                db_deformation_data.insert_one(img_doc)
                print("接收到形变图像数据,ID:",self.ID_str)
            elif data_type == 30:
                db_confidence_data.insert_one(img_doc)
                print("接收到置信度数据,ID:",self.ID_str)
        else:
            print("形变图像数据/置信度数据MD5校验不通过,ID:",self.ID_str)
        return ""

    #更新移动目标数据
    def upload_move_target_data_form_radar(self):
        seq_int = struct.unpack('<I', self.receive_data_byte[0:4])[0]
        md5_str = self.receive_data_byte[4:20].hex()
        #MD5校验
        if hashlib.md5(self.receive_data_byte[20:]).hexdigest() == md5_str:
            time_stamp=struct.unpack('<I', self.receive_data_byte[20:24])[0]
            mission_ID=struct.unpack('<I', self.receive_data_byte[24:28])[0]
            rng_res=struct.unpack('<f', self.receive_data_byte[28:32])[0]
            rng_num=struct.unpack('<I', self.receive_data_byte[32:36])[0]
            rng_min=struct.unpack('<f', self.receive_data_byte[36:40])[0]
            ang_res=struct.unpack('<f', self.receive_data_byte[40:44])[0]
            ang_num=struct.unpack('<I', self.receive_data_byte[44:48])[0]
            ang_min=struct.unpack('<f', self.receive_data_byte[48:52])[0]
            data_type=struct.unpack('<I', self.receive_data_byte[52:56])[0]
            move_target_num=struct.unpack('<I', self.receive_data_byte[56:60])[0]
            move_target_data_size=struct.unpack('<I', self.receive_data_byte[60:64])[0]
            M=rng_num
            N=ang_num
            move_target_data_byte=self.receive_data_byte[64:]
            img_doc = {
                '扫描序号': seq_int,
                '时间戳': time_stamp,
                '任务ID': mission_ID,
                '距离像素间隔': rng_res,
                '距离像素点数量': rng_num,
                '最小距离': rng_min,
                '角度像素间隔': ang_res,
                '角度像素点数量': ang_num,
                '最小角度': ang_min,
                '数据类型': data_type,
                '动目标个数': move_target_num,
            }
            all_target=[]
            for i in range(move_target_num):
                each_target={
                    "目标编号":struct.unpack('<f', move_target_data_byte[0+16*i:4+16*i])[0],
                    "目标角度":struct.unpack('<f', move_target_data_byte[4+16*i:8+16*i])[0],
                    "目标距离":struct.unpack('<f', move_target_data_byte[8+16*i:12+16*i])[0],
                    "目标速度":struct.unpack('<f', move_target_data_byte[12+16*i:16+16*i])[0]
                    }
                all_target.append(each_target)
            img_doc['目标信息']=all_target
            #定位数据库
            db_this_radar=client[self.ID_str]
            db_move_target_data=db_this_radar["move_target_data_"+str(mission_ID)]
            db_move_target_data.insert_one(img_doc)
            print("接收到移动目标数据,ID:",self.ID_str)
        else:
            print("移动目标数据MD5校验不通过,ID:",self.ID_str)
        return ""
    
    #更新雷达log文件
    def add_log_form_radar(self):
        name_str = self.receive_data_byte[:128].decode('ascii').split(',')[0]
        log_road = base_file_path+self.ID_str + "/log_file/"
        print(log_road+name_str)
        with open(log_road+name_str, 'ab') as f:
            f.write(self.receive_data_byte[128:])
            print("接收log文件成功")
        return ""

    #更新雷达时间戳
    def update_radar_time(self):
        data_len_str="04"+"00"*3
        # timestamp_int = int(time.time())
        timestamp_int = int(time.mktime(time.localtime()))      #获取系统时间
        send_data_str = struct.pack('<I', timestamp_int).hex()
        print("时间戳同步完成:",timestamp_int)
        return send_data_str
    
    #极坐标转笛卡尔坐标（python代码）
    def polar2cart(self, params):
        print("开始坐标系转换")
        img = imread(params["img_road"])
        img = np.rot90(img.astype(np.float32))

        # 已知参数
        r_min = params["rng_min"]  # 最近距离 10m
        r_res = params["rng_res"]  # 每个像素代表 0.3m
        theta_min = params["ang_min"]  # 起始角度
        theta_res = params["ang_res"]
        M=params["M"]
        N=params["N"]
        theta_values = np.linspace(theta_min, theta_min + (N - 1) * theta_res, N)  # 角度范围
        r_values = np.linspace(r_min + (M - 1) * r_res, r_min, M)  # 半径范围

        # 生成极坐标网格
        Theta, R = np.meshgrid(theta_values, r_values)

        # 计算对应的笛卡尔坐标
        X = R * np.cos(Theta)
        Y = R * np.sin(Theta)

        # 生成笛卡尔网格 (新的规则网格)
        x_cart = np.linspace(X.min(), X.max(), N)
        y_cart = np.linspace(Y.min(), Y.max(), M)
        X_cart, Y_cart = np.meshgrid(x_cart, y_cart)

        # 插值到笛卡尔坐标
        cartesian_img = griddata((X.flatten(), Y.flatten()), img.flatten(),(X_cart, Y_cart), method='linear', fill_value=0)
        img = 20 * np.log10(np.where(cartesian_img == 0, np.min(cartesian_img[cartesian_img != 0]), cartesian_img)/np.max(cartesian_img))
        img = np.rot90(img,2)
        plt.axis('off')
        # plt.colorbar()
        #找到图片最小值
        min_val = np.min(img)
        alpha = np.ones_like(img)
        alpha[img == min_val] = 0  # 使最低值区域透明
        plt.imshow(img, cmap='jet',  aspect='auto',alpha=alpha)
        plt.savefig(params["save_road"], bbox_inches='tight',pad_inches=0)

    #查询雷达状态
    def query_radar_state(self):
        # global head_hex, con_int, radar_head_hex
        self.con_int = self.con_int + 1
        db_this_radar = client[self.ID_str]

        flag_str="00"
        command_str="02"
        extend_str="00"
        data_con_str=struct.pack('<I', con_int).hex()
        data_len_str='00'*4
        ID_str_send= bytes.fromhex(self.ID_str)[::-1].hex()
        send_code_str=self.head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
        receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
        sock = self.Socket  # 获取对应的 Socket
        sock.sendall(bytes.fromhex(send_code_str))
        print("查询雷达状态指令发送成功")
        #接收指令
        recieve_data_byte = sock.recv(self.receive_len)
        recieve_data_byte_str=recieve_data_byte[:13].hex()
        #匹配接收字节头部
        if recieve_data_byte_str == receive_expect_head:
            radar_work_state = self.judge_state(recieve_data_byte[13:14].hex())
            if radar_work_state == "设置正确":
                #截取数据部分
                self.receive_data=recieve_data_byte[28:]
                self.update_radar_imformation()
                # update_radar_imformation(self.ID_str, receive_data_byte)
                return "success"
            print("雷达工作状态:",radar_work_state)
            return "error"
        print("非对应指令答复")
        return "error"

        





        for temp_radar in radar_list:
            if temp_radar.ID_str == ID_str:
                flag_str="00"
                command_str="02"
                extend_str="00"
                data_con_str=struct.pack('<I', con_int).hex()
                data_len_str='00'*4
                ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
                send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
                receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
                sock = temp_radar.Socket  # 获取对应的 Socket
                sock.sendall(bytes.fromhex(send_code_str))
                print("查询雷达状态指令发送成功")
                #接收指令
                recieve_data_byte = sock.recv(10240000)
                recieve_data_byte_str=recieve_data_byte[:13].hex()
                #匹配接收字节头部
                if recieve_data_byte_str == receive_expect_head:
                    radar_work_state = judge_state(recieve_data_byte[13:14].hex())
                    if radar_work_state == "设置正确":
                        #截取数据部分
                        receive_data_byte=recieve_data_byte[28:]
                        update_radar_imformation(ID_str, receive_data_byte)
                        return "success"
                    print("雷达工作状态:",radar_work_state)
                    return "error"
                print("非对应指令答复")
                return "error"
        print("雷达离线")
        return "error"
    
    #查询场景参数
    def query_scene_parameter(self):
        self.con_int = self.con_int + 1
        db_this_radar = client[self.ID_str]
        db_scene_parameter = db_this_radar['scene_parameter']
        sock = self.Socket  # 获取对应的 Socket

        flag_str="00"
        command_str="01"
        extend_str="00"
        data_con_str=struct.pack('<i', con_int).hex()
        data_len_str='00'*4
        ID_str_send= bytes.fromhex(self.ID_str)[::-1].hex()
        send_code_str=self.head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
        receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
        sock.sendall(bytes.fromhex(send_code_str))
        print("查询场景参数指令发送成功")
        #接收指令
        receive_data_byte = sock.recv(self.receive_len)
        receive_data_byte_str=receive_data_byte[:13].hex()
        #匹配接收字节头部
        if receive_data_byte_str == receive_expect_head:
            radar_work_state = self.judge_state(receive_data_byte[13:14].hex())
            if radar_work_state == "设置正确":
                #截取数据部分
                receive_data_byte=receive_data_byte[28:]
                for scene_parameter_doc in db_scene_parameter.find():
                    start_byte_position=scene_parameter_doc.get('start_byte_position')
                    all_byte_int=scene_parameter_doc.get('all_byte')
                    data_type=scene_parameter_doc.get('data_type')
                    temp_data=''
                    if data_type == 'int16':
                        temp_data = struct.unpack('<h', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                    elif data_type == 'int32':
                        temp_data = struct.unpack('<I', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                    elif data_type == 'float32': 
                        temp_data = struct.unpack('<f', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                    elif data_type == 'float64': 
                        temp_data = struct.unpack('<d', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                    db_scene_parameter.update_one({'start_byte_position': start_byte_position}, {'$set': {'data': temp_data}})
                print("设备场景参数查询成功,ID:",self.ID_str)
                return "success"
            print("雷达工作状态:",radar_work_state)
            return radar_work_state
        return "error"


        for temp_radar in radar_list:
            if temp_radar.ID_str == ID_str:
                sock = temp_radar.Socket  # 获取对应的 Socket
                flag_str="00"
                command_str="01"
                extend_str="00"
                data_con_str=struct.pack('<i', con_int).hex()
                data_len_str='00'*4
                ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
                send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
                receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
                sock.sendall(bytes.fromhex(send_code_str))
                print("查询场景参数指令发送成功")
                #接收指令
                receive_data_byte = sock.recv(10240000)
                receive_data_byte_str=receive_data_byte[:13].hex()
                #匹配接收字节头部
                if receive_data_byte_str == receive_expect_head:
                    radar_work_state = judge_state(receive_data_byte[13:14].hex())
                    if radar_work_state == "设置正确":
                        #截取数据部分
                        receive_data_byte=receive_data_byte[28:]
                        for scene_parameter_doc in db_scene_parameter.find():
                            start_byte_position=scene_parameter_doc.get('start_byte_position')
                            all_byte_int=scene_parameter_doc.get('all_byte')
                            data_type=scene_parameter_doc.get('data_type')
                            temp_data=''
                            if data_type == 'int16':
                                temp_data = struct.unpack('<h', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                            elif data_type == 'int32':
                                temp_data = struct.unpack('<I', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                            elif data_type == 'float32': 
                                temp_data = struct.unpack('<f', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                            elif data_type == 'float64': 
                                temp_data = struct.unpack('<d', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
                            db_scene_parameter.update_one({'start_byte_position': start_byte_position}, {'$set': {'data': temp_data}})
                        print("设备场景参数查询成功,ID:",ID_str)
                        return "success"
                    print("雷达工作状态:",radar_work_state)
                    return radar_work_state
                return "error"
        print("雷达离线")
        return "error"

    #设置雷达场景参数
    def set_scene_parameter_to_radar(self):
        self.con_int = self.con_int + 1
        db_this_radar = client[self.ID_str]
        db_scene_parameter = db_this_radar['scene_parameter']

        sock = self.Socket  # 获取对应的 Socket
        flag_str="00"
        command_str="00"
        extend_str="00"
        data_con_str=struct.pack('<I', con_int).hex()
        ID_str_send= bytes.fromhex(self.ID_str)[::-1].hex()
        
        temp_now_byte_int=0
        send_data_str=''
        for scene_parameter_doc in db_scene_parameter.find():
            parameter_begin_byte=scene_parameter_doc.get('start_byte_position')
            parameter_byte=scene_parameter_doc.get('all_byte')
            parameter_data=scene_parameter_doc.get('data')
            parameter_type=scene_parameter_doc.get('data_type')
            if parameter_type == 'int16':
                parameter_data=struct.pack('<h', parameter_data).hex()
            elif parameter_type == 'int32':
                parameter_data=struct.pack('<I', parameter_data).hex()
            elif parameter_type == 'float32':
                parameter_data=struct.pack('<f', parameter_data).hex()
            elif parameter_type == 'float64':
                parameter_data=struct.pack('<d', parameter_data).hex()

            if temp_now_byte_int<parameter_begin_byte:
                send_data_str=send_data_str+'00'*(parameter_begin_byte-temp_now_byte_int)
            send_data_str=send_data_str+parameter_data
            temp_now_byte_int=parameter_begin_byte+parameter_byte

        data_len_str=struct.pack('<I', temp_now_byte_int).hex()
        print(data_len_str)
        send_code_str=self.head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str+send_data_str
        receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
        sock.sendall(bytes.fromhex(send_code_str))
        recieve_data_byte = sock.recv(self.receive_len)
        recieve_data_byte_str=recieve_data_byte[:13].hex()
        #匹配接收字节头部
        if recieve_data_byte_str == receive_expect_head:
            radar_work_state = self.judge_state(recieve_data_byte[13:14].hex())
            if radar_work_state == "设置正确":
                print("场景参数设置成功")
                return "success"
            print("雷达工作状态:",radar_work_state)
            return radar_work_state
        print("非对应指令答复")
        return "error"
    
        for temp_radar in radar_list:
            if temp_radar.ID_str == ID_str:
                sock = temp_radar.Socket  # 获取对应的 Socket
                flag_str="00"
                command_str="00"
                extend_str="00"
                data_con_str=struct.pack('<I', con_int).hex()
                ID_str_send= bytes.fromhex(ID_str)[::-1].hex()

                temp_now_byte_int=0
                send_data_str=''
                for scene_parameter_doc in db_scene_parameter.find():
                    parameter_begin_byte=scene_parameter_doc.get('start_byte_position')
                    parameter_byte=scene_parameter_doc.get('all_byte')
                    parameter_data=scene_parameter_doc.get('data')
                    parameter_type=scene_parameter_doc.get('data_type')
                    if parameter_type == 'int16':
                        parameter_data=struct.pack('<h', parameter_data).hex()
                    elif parameter_type == 'int32':
                        parameter_data=struct.pack('<I', parameter_data).hex()
                    elif parameter_type == 'float32':
                        parameter_data=struct.pack('<f', parameter_data).hex()
                    elif parameter_type == 'float64':
                        parameter_data=struct.pack('<d', parameter_data).hex()

                    if temp_now_byte_int<parameter_begin_byte:
                        send_data_str=send_data_str+'00'*(parameter_begin_byte-temp_now_byte_int)
                    send_data_str=send_data_str+parameter_data
                    temp_now_byte_int=parameter_begin_byte+parameter_byte

                data_len_str=struct.pack('<I', temp_now_byte_int).hex()
                print(data_len_str)
                send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str+send_data_str
                receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
                sock.sendall(bytes.fromhex(send_code_str))
                recieve_data_byte = sock.recv(10240000)
                recieve_data_byte_str=recieve_data_byte[:13].hex()
                #匹配接收字节头部
                if recieve_data_byte_str == receive_expect_head:
                    radar_work_state = judge_state(recieve_data_byte[13:14].hex())
                    if radar_work_state == "设置正确":
                        print("场景参数设置成功")
                        return "success"
                    print("雷达工作状态:",radar_work_state)
                    return radar_work_state
                print("非对应指令答复")
                return "error"
        print("雷达离线")
        return "error"


    #判断指令执行状态
    def judge_state(self,state_str):
        running_state_map={
            "00":"设置正确",
            "01":"参数错误",
            "02":"数据长度错误",
            "03":"重发",
            "04":"雷达正在加载参数,暂时不能开始工作",
            "05":"处于工作状态,设置失败"
        }
        return running_state_map.get(state_str)

    #雷达控制，发送指令
    # extend_int：
    # 0：开始工作
    # 1：停止工作
    # 2：重启雷达
    def radar_work_control(self, extend_int):
        self.con_int = self.con_int + 1
        db_this_radar=client[self.ID_str]
        db_scene_parameter = db_this_radar['scene_parameter']

        flag_str="00"
        command_str="03"
        extend_str=struct.pack('<B', extend_int).hex()
        data_con_str=struct.pack('<I', con_int).hex()
        data_len_str='00'*4
        ID_str_send= bytes.fromhex(self.ID_str)[::-1].hex()
        send_code_str=self.head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
        receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
        sock = self.Socket  # 获取对应的 Socket
        sock.sendall(bytes.fromhex(send_code_str))
        print("工作指令发送成功")

        #接收指令
        recieve_data_byte = sock.recv(self.receive_len)
        recieve_data_byte_str=recieve_data_byte[:13].hex()
        #匹配接收字节头部
        if recieve_data_byte_str == receive_expect_head:
            radar_work_state = self.judge_state(recieve_data_byte[13:14].hex())
            if radar_work_state == "设置正确":
                this_radar_state = db_base_data_radar.find_one({"ID": self.ID_str})
                #开始工作
                if extend_int == 0:
                    mission_ID_doc=db_scene_parameter.find_one({"code_name":"MissionID"})
                    mission_ID=str(mission_ID_doc["data"])
                    #更新数据库任务列表
                    if not db_base_data_radar.find_one({"ID": self.ID_str, "mission_ID.ID": mission_ID}):
                        #任务命名为当前时间
                        time_now_str = datetime.now().strftime('%Y-%m-%d %H:%M')
                        now_coordinates = this_radar_state["radar_coordinates"]
                        radar_zero_theta = db_scene_parameter.find_one({"code_name":"RadarOri"})
                        db_base_data_radar.update_one(
                            {"ID": self.ID_str},
                            {"$addToSet": {"mission_ID": {"ID": mission_ID, "name": time_now_str, "coordinates":now_coordinates, "radar_zero_theta":radar_zero_theta}}}
                        )

                    path_file = base_file_path+ self.ID_str+"/work_data/"+mission_ID
                    if not os.path.exists(path_file):
                        os.makedirs(path_file+"/image_data")
                        os.makedirs(path_file+"/deformation_data")
                        os.makedirs(path_file+"/Confidence_data")
                        print("任务ID:",mission_ID_doc["data"],"数据文件夹创建成功")
                    db_base_data_radar.update_one({'ID': self.ID_str}, {'$set': {'is_work': 1}})
                elif extend_int == 1:
                    db_base_data_radar.update_one({'ID': self.ID_str}, {'$set': {'is_work': 0}})
                print("设置成功")
                return "success"
            print("雷达工作状态:",radar_work_state)
            return radar_work_state
        else:
            print("非对应指令答复")
            return "error"


        #查找对应套接字
        for temp_radar in radar_list:
            if temp_radar.ID_str == ID_str:
                flag_str="00"
                command_str="03"
                extend_str=struct.pack('<B', extend_int).hex()
                data_con_str=struct.pack('<I', con_int).hex()
                data_len_str='00'*4
                ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
                send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
                receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
                sock = temp_radar.Socket  # 获取对应的 Socket
                sock.sendall(bytes.fromhex(send_code_str))
                print("工作指令发送成功")
                #接收指令
                recieve_data_byte = sock.recv(10240000)
                recieve_data_byte_str=recieve_data_byte[:13].hex()
                #匹配接收字节头部
                if recieve_data_byte_str == receive_expect_head:
                    radar_work_state = judge_state(recieve_data_byte[13:14].hex())
                    if radar_work_state == "设置正确":
                        this_radar_state = db_base_data_radar.find_one({"ID_str": ID_str})
                        #开始工作
                        if extend_int == 0:
                            mission_ID_doc=db_scene_parameter.find_one({"code_name":"MissionID"})
                            mission_ID=str(mission_ID_doc["data"])
                            #更新数据库任务列表
                            if not db_base_data_radar.find_one({"ID": ID_str, "mission_ID.ID": mission_ID}):
                                #任务命名为当前时间
                                time_now_str = datetime.now().strftime('%Y-%m-%d %H:%M')
                                db_base_data_radar.update_one(
                                    {"ID": ID_str},
                                    {"$addToSet": {"mission_ID": {"ID": mission_ID, "name": time_now_str}}}
                                )

                            path_file = base_file_path+ID_str+"/work_data/"+mission_ID
                            if not os.path.exists(path_file):
                                os.makedirs(path_file+"/image_data")
                                os.makedirs(path_file+"/deformation_data")
                                os.makedirs(path_file+"/Confidence_data")
                                print("任务ID:",mission_ID_doc["data"],"数据文件夹创建成功")
                            db_base_data_radar.update_one({'ID': ID_str}, {'$set': {'is_work': 1}})
                        elif extend_int == 1:
                            db_base_data_radar.update_one({'ID': ID_str}, {'$set': {'is_work': 0}})
                        print("设置成功")
                        return "success"
                    print("雷达工作状态:",radar_work_state)
                    return radar_work_state
                else:
                    print("非对应指令答复")
                    return "error"
                break
        print("雷达离线")
        return "error"

    #雷达离线
    def radar_disconn(self):
        if self.ID_str:
            db_base_data_radar.update_one({'ID': self.ID_str}, {'$set': {'is_online': 0}})
        self.Socket.close()
        self.Socket = None

        print("雷达离线,ID:",self.ID_str)

#TCP服务器
def tcp_server():
    global stop_server, send_sign, ArcSAR_map
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        global radar_list
        s.bind((HOST, PORT))
        s.listen()
        sockets_list = [s]
        print(f"Server listening on {HOST}:{PORT}")
        
        while not stop_server:
            while send_sign:
                a=1
            read_sockets, _, _ = select.select(sockets_list, [], [], 1)
            for sock in read_sockets:
                if sock == s:
                    # 有新客户端连接
                    conn, addr = s.accept()
                    client = ArcSAR(conn)
                    ArcSAR_map[conn] = client
                    sockets_list.append(conn)
                    print('Connected by', addr)
                else:
                    # 处理客户端数据
                    client = ArcSAR_map[sock]
                    try:
                        data = client.radar_report()
                    except (ConnectionResetError, ConnectionAbortedError) as e:
                        print(f"连接被强制关闭: {e}")
                        data=b''
                    except Exception as e:
                        print(f"其他异常: {e}")
                        b''
                    if data:
                        print('Received from client:', data)
                        # 处理数据
                    else:
                        # print('Client disconnected', client.addr)
                        # new_radar_disconn(sock)
                        sockets_list.remove(sock)
                        client.radar_disconn()
                        if(client.ID_str):
                            del ArcSAR_id_map[client.ID_str]
                        del ArcSAR_map[sock]
                    # data_byte = sock.recv(10240000)
                    # # print("接收到数据")
                    # if data_byte:
                    #     radar_report(data_byte, sock)
                    # else:
                    #     new_radar_disconn(sock)
                    #     # 客户端断开连接
                    #     sockets_list.remove(sock)
                    #     sock.close()
                    #     print('Client disconnected')


def server_control():
    global stop_server, send_sign
    while True:
        send_sign = False
        user_input = input("Type 'exit' to stop the server: ").strip().lower()
        send_sign = True
        if user_input == 'exit':
            # 关闭服务器
            stop_server = True
            print("Shutting down server...")
            # 这里可以添加关闭服务器的逻辑
            break
        try:
            number = int(user_input)
            # platform_command(number,ID_str,'')
        except ValueError:
            # 关闭服务器
            stop_server = True
            print("Shutting down server...")
            # 这里可以添加关闭服务器的逻辑
            break


# def platform_command(function_number, ID_str, file_name_str):
#     global radar_list, head_hex, con_int, radar_head_hex
#     function_map = {
#     "send_query_to_radar": send_query_to_radar,
#     "send_scene_parameter_to_radar": send_scene_parameter_to_radar,
#     "send_algorithm_to_radar":send_algorithm_to_radar,
#     "download_log_file_form_radar": download_log_file_form_radar
#     }
#     db_this_radar = client[ID_str]
#     db_platform_command = db_this_radar['platform_command']
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             doc_function_name = db_platform_command.find_one({'serial_number': function_number})
#             function_name = doc_function_name["function_name"]
#             command_int = doc_function_name["command_code"]
#             extend_int = doc_function_name["extend_code"]
#             print("执行函数：",function_name)
#             if function_name == "download_log_file_form_radar":
#                 con_int=con_int+1
#                 download_log_file_form_radar(head_hex, ID_str, con_int, file_name_str, sock)
#             elif function_name in function_map:
#                 con_int=con_int+1
#                 function_map[function_name](command_int, extend_int, ID_str, sock, head_hex, con_int)  # 调用函数
#                 print("指令发送成功")
#             else:
#                 print(f"Function '{function_name}' not found.")
#             result_mess=receive_platform_command_respond(command_int, extend_int, ID_str, sock, radar_head_hex, con_int, function_number)
#             return result_mess  # 发送完毕后退出循环
#     print("当前雷达不在线")
#     return "当前雷达不在线"
    



# #设置雷达场景参数
# def set_scene_parameter_to_radar(ID_str):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar = client[ID_str]
#     db_scene_parameter = db_this_radar['scene_parameter']
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             flag_str="00"
#             command_str="00"
#             extend_str="00"
#             data_con_str=struct.pack('<I', con_int).hex()
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()

#             temp_now_byte_int=0
#             send_data_str=''
#             for scene_parameter_doc in db_scene_parameter.find():
#                 parameter_begin_byte=scene_parameter_doc.get('start_byte_position')
#                 parameter_byte=scene_parameter_doc.get('all_byte')
#                 parameter_data=scene_parameter_doc.get('data')
#                 parameter_type=scene_parameter_doc.get('data_type')
#                 if parameter_type == 'int16':
#                     parameter_data=struct.pack('<h', parameter_data).hex()
#                 elif parameter_type == 'int32':
#                     parameter_data=struct.pack('<I', parameter_data).hex()
#                 elif parameter_type == 'float32':
#                     parameter_data=struct.pack('<f', parameter_data).hex()
#                 elif parameter_type == 'float64':
#                     parameter_data=struct.pack('<d', parameter_data).hex()

#                 if temp_now_byte_int<parameter_begin_byte:
#                     send_data_str=send_data_str+'00'*(parameter_begin_byte-temp_now_byte_int)
#                 send_data_str=send_data_str+parameter_data
#                 temp_now_byte_int=parameter_begin_byte+parameter_byte

#             data_len_str=struct.pack('<I', temp_now_byte_int).hex()
#             print(data_len_str)
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str+send_data_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock.sendall(bytes.fromhex(send_code_str))
#             recieve_data_byte = sock.recv(10240000)
#             recieve_data_byte_str=recieve_data_byte[:13].hex()
#             #匹配接收字节头部
#             if recieve_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(recieve_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     print("场景参数设置成功")
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return radar_work_state
#             print("非对应指令答复")
#             return "error"
#     print("雷达离线")
#     return "error"

# #设置算法参数
# def set_algorithm_to_radar(ID_str):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar = client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     file_address=db_file_address.find_one({"ID_str":ID_str})
#     if file_address:
#         algorithm_road = file_address.get('Algorithm_road')
#     else:
#         return "error"
#     with open(algorithm_road, 'rb') as f:
#         data_byte = f.read()

#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             flag_str="00"
#             command_str="00"
#             extend_str="01"
#             data_con_str=struct.pack('<i', con_int).hex()
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
            
#             data_len_int = len(data_byte)
#             data_len_str=struct.pack('<i', data_len_int).hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock.sendall(bytes.fromhex(send_code_str)+data_byte)
#             recieve_data_byte = sock.recv(10240000)
#             recieve_data_byte_str=recieve_data_byte[:13].hex()
#             #匹配接收字节头部
#             if recieve_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(recieve_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     print("算法参数设置成功")
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return radar_work_state
#             print("非对应指令答复")
#             return "error"
#     print("雷达离线")
#     return "error"


# #查询场景参数
# def query_scene_parameter(ID_str):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar = client[ID_str]
#     db_scene_parameter = db_this_radar['scene_parameter']
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             flag_str="00"
#             command_str="01"
#             extend_str="00"
#             data_con_str=struct.pack('<i', con_int).hex()
#             data_len_str='00'*4
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock.sendall(bytes.fromhex(send_code_str))
#             print("查询场景参数指令发送成功")
#             #接收指令
#             receive_data_byte = sock.recv(10240000)
#             receive_data_byte_str=receive_data_byte[:13].hex()
#             #匹配接收字节头部
#             if receive_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(receive_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     #截取数据部分
#                     receive_data_byte=receive_data_byte[28:]
#                     for scene_parameter_doc in db_scene_parameter.find():
#                         start_byte_position=scene_parameter_doc.get('start_byte_position')
#                         all_byte_int=scene_parameter_doc.get('all_byte')
#                         data_type=scene_parameter_doc.get('data_type')
#                         temp_data=''
#                         if data_type == 'int16':
#                             temp_data = struct.unpack('<h', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#                         elif data_type == 'int32':
#                             temp_data = struct.unpack('<I', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#                         elif data_type == 'float32': 
#                             temp_data = struct.unpack('<f', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#                         elif data_type == 'float64': 
#                             temp_data = struct.unpack('<d', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#                         db_scene_parameter.update_one({'start_byte_position': start_byte_position}, {'$set': {'data': temp_data}})
#                     print("设备场景参数查询成功,ID:",ID_str)
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return radar_work_state
#             return "error"
#     print("雷达离线")
#     return "error"


# #查询算法参数
# def query_algorithm(ID_str):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar = client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     file_address=db_file_address.find_one({"ID":ID_str})
#     if file_address:
#         algorithm_road = file_address.get('Algorithm_road')
#     else:
#         print("未找到对应文件地址")
#         return "error"
#     #查找对应套接字
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             flag_str="00"
#             command_str="01"
#             extend_str="01"
#             data_con_str=struct.pack('<I', con_int).hex()
#             data_len_str='00'*4
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock.sendall(bytes.fromhex(send_code_str))
#             print("查询算法参数指令发送成功")
#             #接收指令
#             receive_data_byte = sock.recv(10240000)
#             receive_data_byte_str=receive_data_byte[:13].hex()
#             #匹配接收字节头部
#             if receive_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(receive_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     #截取数据部分
#                     receive_data_byte=receive_data_byte[28:]
#                     with open(algorithm_road, 'wb') as f:
#                         f.write(receive_data_byte)
#                         print("成功获取算法文件")
#                         return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return "error"
#             print("非对应指令答复")
#             return "error"
#     print("雷达离线")
#     return "error"


# #查询雷达状态
# def query_radar_state(ID_str):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar = client[ID_str]
#     db_radar_information = db_this_radar['radar_information']
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             flag_str="00"
#             command_str="02"
#             extend_str="00"
#             data_con_str=struct.pack('<I', con_int).hex()
#             data_len_str='00'*4
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             sock.sendall(bytes.fromhex(send_code_str))
#             print("查询雷达状态指令发送成功")
#             #接收指令
#             recieve_data_byte = sock.recv(10240000)
#             recieve_data_byte_str=recieve_data_byte[:13].hex()
#             #匹配接收字节头部
#             if recieve_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(recieve_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     #截取数据部分
#                     receive_data_byte=recieve_data_byte[28:]
#                     update_radar_imformation(ID_str, receive_data_byte)
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return "error"
#             print("非对应指令答复")
#             return "error"
#     print("雷达离线")
#     return "error"


# #雷达控制，发送指令
# # extend_int：
# # 0：开始工作
# # 1：停止工作
# # 2：重启雷达
# def radar_work_control(ID_str, extend_int):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar=client[ID_str]
#     db_scene_parameter = db_this_radar['scene_parameter']
#     #查找对应套接字
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             flag_str="00"
#             command_str="03"
#             extend_str=struct.pack('<B', extend_int).hex()
#             data_con_str=struct.pack('<I', con_int).hex()
#             data_len_str='00'*4
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             sock.sendall(bytes.fromhex(send_code_str))
#             print("工作指令发送成功")
#             #接收指令
#             recieve_data_byte = sock.recv(10240000)
#             recieve_data_byte_str=recieve_data_byte[:13].hex()
#             #匹配接收字节头部
#             if recieve_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(recieve_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     this_radar_state = db_base_data_radar.find_one({"ID_str": ID_str})
#                     #开始工作
#                     if extend_int == 0:
#                         mission_ID_doc=db_scene_parameter.find_one({"code_name":"MissionID"})
#                         mission_ID=str(mission_ID_doc["data"])
#                         #更新数据库任务列表
#                         if not db_base_data_radar.find_one({"ID": ID_str, "mission_ID.ID": mission_ID}):
#                             #任务命名为当前时间
#                             time_now_str = datetime.now().strftime('%Y-%m-%d %H:%M')
#                             db_base_data_radar.update_one(
#                                 {"ID": ID_str},
#                                 {"$addToSet": {"mission_ID": {"ID": mission_ID, "name": time_now_str}}}
#                             )

#                         path_file = base_file_path+ID_str+"/work_data/"+mission_ID
#                         if not os.path.exists(path_file):
#                             os.makedirs(path_file+"/image_data")
#                             os.makedirs(path_file+"/deformation_data")
#                             os.makedirs(path_file+"/Confidence_data")
#                             print("任务ID:",mission_ID_doc["data"],"数据文件夹创建成功")
#                         db_base_data_radar.update_one({'ID': ID_str}, {'$set': {'is_work': 1}})
#                     elif extend_int == 1:
#                         db_base_data_radar.update_one({'ID': ID_str}, {'$set': {'is_work': 0}})
#                     print("设置成功")
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return radar_work_state
#             else:
#                 print("非对应指令答复")
#                 return "error"
#             break
#     print("雷达离线")
#     return "error"



# #大气校正开关控制
# def atmospheric_correction_control(ID_str, state):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1

#     #查找对应套接字
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             flag_str="00"
#             command_str="05"
#             extend_str=struct.pack('<B', state).hex()
#             data_con_str=struct.pack('<I', con_int).hex()
#             data_len_str='00'*4
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock.sendall(bytes.fromhex(send_code_str))
#             print("大气校正开关控制发送成功")
#             #接收指令
#             recieve_data_byte = sock.recv(10240000)
#             recieve_data_byte_str=recieve_data_byte[:13].hex()
#             #匹配接收字节头部
#             if recieve_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(recieve_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     print("设置成功")
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return radar_work_state
#             else:
#                 print("非对应指令答复")
#                 return "error"
#             break
#     print("非对应指令答复")
#     return "error"

    
# def judge_state(state_str):
#     running_state_map={
#         "00":"设置正确",
#         "01":"参数错误",
#         "02":"数据长度错误",
#         "03":"重发",
#         "04":"雷达正在加载参数,暂时不能开始工作",
#         "05":"处于工作状态,设置失败"
#     }
#     return running_state_map.get(state_str)


# #查询log内容
# def receive_log_form_radar(ID_str):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar=client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             flag_str="00"
#             command_str="08"
#             extend_str="00"
#             data_con_str=struct.pack('<I', con_int).hex()
#             data_len_str='00'*4
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock.sendall(bytes.fromhex(send_code_str))
#             print("查询log内容发送成功")
#             #接收指令
#             recieve_data_byte = sock.recv(10240000)
#             recieve_data_byte_str=recieve_data_byte[:13].hex()
#             #匹配接收字节头部
#             if recieve_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(recieve_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     #截取数据部分
#                     receive_data_byte=recieve_data_byte[28:]
#                     all_log_str=receive_data_byte.decode('ascii')
#                     log_str=all_log_str.split(',')
#                     if len(all_log_str):
#                         for each_log in log_str:
#                             if db_file_address.find_one({"log_name": each_log}) is None:
#                                 new_log = {
#                                     'log_name': each_log,
#                                     'log_road': base_file_path+ID_str+"/radar_file/"+each_log,
#                                     'up_to_date': 0, 
#                                     'type': "log文件"
#                                 }
#                                 if not each_log.endswith(".log"):
#                                     new_log['type']="文件夹"
#                                 db_file_address.insert_one(new_log)
#                             else:
#                                 db_file_address.update_one({"log_name": each_log}, {'$set': {'up_to_date': 0}})
#                     print("log文件目录更新完成")
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return radar_work_state
#             print("非对应指令答复")
#             return "error"
#     print("雷达离线")
#     return "error"


# #下载log文件
# def download_log_file(ID_str, file_name_str):
#     global head_hex, con_int, radar_head_hex
#     con_int = con_int + 1
#     db_this_radar=client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     for temp_radar in radar_list:
#         if temp_radar.ID_str == ID_str:
#             sock = temp_radar.Socket  # 获取对应的 Socket
#             command_str="08"
#             extend_str="01"
#             flag_str="00"
#             data_con_str=struct.pack('<i', con_int).hex()
#             ID_str_send= bytes.fromhex(ID_str)[::-1].hex()
#             file_name_str_ascii=file_name_str.encode('ascii').hex()
#             data_len_str=struct.pack('<i', len(file_name_str)).hex()
#             send_code_str=head_hex+ID_str_send+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str+file_name_str_ascii
#             receive_expect_head = radar_head_hex + ID_str_send+command_str+extend_str+"01"+data_con_str
#             sock.sendall(bytes.fromhex(send_code_str))
#             print("下载log文件指令发送成功")
#             receive_data_byte = sock.recv(10240000)
#             receive_data_byte_str=receive_data_byte[:13].hex()
#             #匹配接收字节头部
#             if receive_data_byte_str == receive_expect_head:
#                 radar_work_state = judge_state(receive_data_byte[13:14].hex())
#                 if radar_work_state == "设置正确":
#                     #截取数据部分
#                     receive_data_byte=receive_data_byte[28:]
#                     name_str = receive_data_byte[:128].decode('ascii').split(',')[0]
#                     log_mess = db_file_address.find_one({"log_name": name_str})
#                     log_road = log_mess['log_road']
#                     with open(log_road, 'wb') as f:
#                         f.write(receive_data_byte[128:])
#                         print("成功获取log文件")
#                     db_file_address.update_one({"log_name": name_str}, {'$set': {'up_to_date': 1}})
#                     return "success"
#                 print("雷达工作状态:",radar_work_state)
#                 return radar_work_state
#             print("非对应指令答复")
#             return "error"
#     print("雷达离线")
#     return "error"

# def receive_platform_command_respond(command_int, extend_int, ID_str, sock, radar_head_hex, con_int, function_number):
#     running_status_map={
#         "00":"设置正确",
#         "01":"参数错误",
#         "02":"数据长度错误",
#         "03":"重发",
#         "04":"雷达正在加载参数,暂时不能开始工作",
#         "05":"处于工作状态,设置失败"
#     }
#     respond_function_map = {
#         "this": "this",
#         "update_scene_parameter": update_scene_parameter,
#         "update_radar_imformation": update_radar_imformation,
#         "update_log_directory":update_log_directory,
#         "update_algorithm_form_radar":update_algorithm_form_radar,
#         "receive_log_form_radar":receive_log_form_radar
#     }
#     try:
#         #等待两秒内应答
#         # sock.settimeout(5)
#         data_byte = sock.recv(10240000)
#         respond_flag_str="01"
#         temp_ID_str= bytes.fromhex(ID_str)[::-1].hex()
#         command_str=struct.pack('<B', command_int).hex()
#         extend_str=struct.pack('<B', extend_int).hex()
#         data_con_str=struct.pack('<I', con_int).hex()
#         receive_data_header=radar_head_hex+temp_ID_str+command_str+extend_str+respond_flag_str+data_con_str
#         print("接收到雷达响应:",data_byte.hex())
#         #判断头部
#         if data_byte[0:13] == bytes.fromhex(receive_data_header):
#             print("响应头部匹配")
#             running_status_str = data_byte[13:14].hex()
#             if running_status_str == "00":
#                 print("执行状态匹配")
#                 db_this_radar = client[ID_str]
#                 db_platform_command = db_this_radar['platform_command']
#                 data_len_int=int(data_byte[24:28][::-1].hex(),16)
#                 receive_data_byte=data_byte[28:28+data_len_int]
#                 doc_function_name = db_platform_command.find_one({'serial_number': function_number})
#                 respond_function_name = doc_function_name["respond_function_name"]
#                 print("执行函数：",respond_function_name)
#                 if respond_function_name == "this":
#                     print(running_status_map.get(running_status_str))
#                     # return running_status_map.get(running_status_str)
#                 elif respond_function_name in respond_function_map:
#                     respond_function_map[respond_function_name](ID_str, receive_data_byte)
#                 print("接收雷达响应完成")
#                 return "success"
#             else:
#                 print("执行状态:",running_status_map[running_status_str])
#                 return running_status_map.get(running_status_str)
#     except socket.timeout:
#         print("雷达没有回应")
#         return "雷达没有回应"
#     except Exception as e:
#         print(f"其他错误: {e}")
#         return "其他错误"


# def update_log_directory(ID_str, receive_data_byte):
#     db_this_radar=client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     all_log_str=receive_data_byte.decode('ascii')
#     log_str=all_log_str.split(',')
#     if len(all_log_str):
#         for each_log in log_str:
#             if db_file_address.find_one({"log_name": each_log}) is None:
#                 new_log = {
#                     'log_name': each_log,
#                     'log_road': base_file_path+ID_str+"/radar_file/"+each_log,
#                     'up_to_date': 0, 
#                     'type': "log文件"
#                 }
#                 if not each_log.endswith(".log"):
#                     new_log['type']="文件夹"
#                 db_file_address.insert_one(new_log)
#             else:
#                 db_file_address.update_one({"log_name": each_log}, {'$set': {'up_to_date': 0}})


# def update_algorithm_form_radar(ID_str, receive_data_byte):
#     db_this_radar=client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     file_address=db_file_address.find_one({"ID":ID_str})
#     if file_address:
#         algorithm_road = file_address.get('Algorithm_road')
#     else:
#         return "error"
    
#     with open(algorithm_road, 'wb') as f:
#         f.write(receive_data_byte)
#         print("成功获取算法文件")


# def receive_log_form_radar(ID_str, receive_data_byte):
#     db_this_radar=client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     name_str = receive_data_byte[:128].decode('ascii').split(',')[0]
#     log_mess = db_file_address.find_one({"log_name": name_str})
#     log_road = log_mess['log_road']
#     with open(log_road, 'wb') as f:
#         f.write(receive_data_byte[128:])
#         print("成功获取log文件")
#     db_file_address.update_one({"log_name": name_str}, {'$set': {'up_to_date': 1}})


# def send_query_to_radar(command_int, extend_int, ID_str, sock, head_hex, con_int):
#     flag_str="00"
#     command_str=struct.pack('<B', command_int).hex()
#     extend_str=struct.pack('<B', extend_int).hex()
#     data_con_str=struct.pack('<I', con_int).hex()
#     data_len_str='00'*4
#     ID_str= bytes.fromhex(ID_str)[::-1].hex()
#     send_code_str=head_hex+ID_str+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#     print(send_code_str)
#     sock.sendall(bytes.fromhex(send_code_str))


# def send_scene_parameter_to_radar(command_int, extend_int, ID_str, sock, head_hex, con_int):
#     db_this_radar = client[ID_str]
#     db_scene_parameter = db_this_radar['scene_parameter']
    
#     flag_str="00"
#     command_str=struct.pack('<B', command_int).hex()
#     extend_str=struct.pack('<B', extend_int).hex()
#     data_con_str=struct.pack('<I', con_int).hex()
#     ID_str= bytes.fromhex(ID_str)[::-1].hex()

#     temp_now_byte_int=0
#     send_data_str=''
#     for scene_parameter_doc in db_scene_parameter.find():
#         parameter_begin_byte=scene_parameter_doc.get('start_byte_position')
#         parameter_byte=scene_parameter_doc.get('all_byte')
#         parameter_data=scene_parameter_doc.get('data')
#         parameter_type=scene_parameter_doc.get('data_type')
#         if parameter_type == 'int16':
#             parameter_data=struct.pack('<h', parameter_data).hex()
#         elif parameter_type == 'int32':
#             parameter_data=struct.pack('<I', parameter_data).hex()
#         elif parameter_type == 'float32':
#             parameter_data=struct.pack('<f', parameter_data).hex()
#         elif parameter_type == 'float64':
#             parameter_data=struct.pack('<d', parameter_data).hex()

#         if temp_now_byte_int<parameter_begin_byte:
#             send_data_str=send_data_str+'00'*(parameter_begin_byte-temp_now_byte_int)
#         send_data_str=send_data_str+parameter_data
#         temp_now_byte_int=parameter_begin_byte+parameter_byte

#     data_len_str=struct.pack('<I', temp_now_byte_int).hex()
#     print(data_len_str)
#     send_code_str=head_hex+ID_str+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str+send_data_str
#     sock.sendall(bytes.fromhex(send_code_str))


# def send_algorithm_to_radar(command_int, extend_int, ID_str, sock, head_hex, con_int):
#     db_this_radar = client[ID_str]
#     db_file_address = db_this_radar['file_address']
#     file_address=db_file_address.find_one({"ID_str":ID_str})
#     if file_address:
#         algorithm_road = file_address.get('Algorithm_road')
#     else:
#         return "error"
#     with open(algorithm_road, 'rb') as f:
#         data_byte = f.read()
#     flag_str="00"
#     command_str=struct.pack('<B', command_int).hex()
#     extend_str=struct.pack('<B', extend_int).hex()
#     data_con_str=struct.pack('<i', con_int).hex()
#     ID_str= bytes.fromhex(ID_str)[::-1].hex()
    
#     data_len_int = len(data_byte)
#     data_len_str=struct.pack('<i', data_len_int).hex()
#     send_code_str=head_hex+ID_str+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str
#     sock.sendall(bytes.fromhex(send_code_str)+data_byte)


# def download_log_file_form_radar(head_hex, ID_str, con_int, file_name_str, sock):
#     temp_ID_str= bytes.fromhex(ID_str)[::-1].hex()
#     command_str="08"
#     extend_str="01"
#     flag_str="00"
#     data_con_str=struct.pack('<i', con_int).hex()
#     file_name_str_ascii=file_name_str.encode('ascii').hex()
#     data_len_str=struct.pack('<i', len(file_name_str)).hex()
#     send_code_str=head_hex+temp_ID_str+command_str+extend_str+flag_str+data_con_str+"00"*11+data_len_str+file_name_str_ascii
#     sock.sendall(bytes.fromhex(send_code_str))


# def sent_radar(ID_str, command_str, extend_str, flag_str, con_str, data_len_str, send_data_str, sock):
#     global head_hex
#     ID_str= bytes.fromhex(ID_str)[::-1].hex() #转为小端在前
#     send_code_str=head_hex+ID_str+command_str+extend_str+flag_str+con_str+"00"*11+data_len_str
#     if data_len_str!=("00"*4):
#         send_code_str=send_code_str+send_data_str
#     sock.sendall(bytes.fromhex(send_code_str))


# #解析雷达上报指令
# def radar_report(data_byte, sock):
#     extend_str_map={
#         "00":update_radar_imformation,
#         "01":upload_image_data_form_radar,
#         "02":upload_deformation_or_confidence_data_form_radar,
#         "03":upload_deformation_or_confidence_data_form_radar,
#         "04":upload_move_target_data_form_radar,
#         "05":add_log_form_radar,
#         "06":update_radar_time
#     }
#     global radar_list, radar_head_hex, head_hex, stop_server
#     head_str=data_byte[0:2][::-1].hex()
#     ID_str=data_byte[2:6][::-1].hex()
#     command_str=data_byte[6:7][::-1].hex()
#     extend_str=data_byte[7:8][::-1].hex()
#     sign_str=data_byte[8:9][::-1].hex()
#     con_int=int(data_byte[9:13][::-1].hex(),16)
#     data_len_int=int(data_byte[24:28][::-1].hex(),16)
#     receive_data_byte=data_byte[28:28+data_len_int]

#     #判断是否雷达第一次连接
#     if not any(temp_radar.ID_str == ID_str for temp_radar in radar_list):
#         new_radar_conn(ID_str, sock)

#     print("接收到雷达上报指令",head_str)
#     # print("head:",head_str)
#     # print("ID:",ID_str)
#     print("command:",command_str)
#     print("extend:",extend_str)
#     # print("sign:",sign_str)
#     # print("con:",con_int)
#     print("data_len:",data_len_int)
#     # print("receive_data:", receive_data_byte.hex())

#     #接收完整数据
#     if int(extend_str)>=1 and int(extend_str)<=5 and int(command_str)==40:
#         print("接收到数据,当前字节数为：",len(receive_data_byte))
#         while len(receive_data_byte) < data_len_int:
#             chunk = sock.recv(10240000)
#             receive_data_byte += chunk
#             print("接收到数据,当前字节数为：",len(receive_data_byte))

#     #判断是否为雷达主动上报的指令
#     if radar_head_hex == head_str and command_str == "40" and (extend_str in extend_str_map):
#         return_data_str = extend_str_map[extend_str](ID_str, receive_data_byte)
#         data_len_int = int(len(return_data_str)/2)
#         data_len_str = struct.pack('<i', data_len_int).hex()
#         send_data_str = head_hex+data_byte[2:8].hex()+"01"+data_byte[9:24].hex()+data_len_str
#         if data_len_int != 0:
#             send_data_str=send_data_str+return_data_str
#         sock.sendall(bytes.fromhex(send_data_str))
#     else:
#         print("非雷达主动上报的指令或扩展码错误")
        

# #雷达在线连接
# def new_radar_conn(ID_str,sock):
#     global radar_list
#     radar_list.append(Radar(ID_str=ID_str, Socket=sock))
#     base_radar_data = db_base_data_radar.find_one({"ID": ID_str})
#     if base_radar_data is None:
#         print("新雷达连接,ID：",ID_str)
#         db_base_data_radar.insert_one({
#             "ID": ID_str,
#             "is_online": 1,
#             "is_work":0,
#             })
#         radar_file_path = base_file_path+ID_str
#         if not os.path.exists(radar_file_path):  # 检查文件夹是否已存在
#             os.makedirs(radar_file_path+"/algorithm_file")
#             os.makedirs(radar_file_path+"/log_file")
#             os.makedirs(radar_file_path+"/work_data")
#             print("雷达文件夹创建成功")
        

#         #雷达基本信息数据库创建
#         db_radar=client[ID_str]
#         df_radar_information = pd.read_excel('data.xlsx', sheet_name='雷达信息')
#         data_radar_information = df_radar_information.to_dict(orient='records')
#         db_radar_information = db_radar["radar_information"]
#         db_radar_information.insert_many(data_radar_information)
#         df_scene_parameter = pd.read_excel('data.xlsx', sheet_name='场景参数')
#         data_scene_parameter = df_scene_parameter.to_dict(orient='records')
#         db_scene_parameter = db_radar["scene_parameter"]
#         db_scene_parameter.insert_many(data_scene_parameter)
#         df_platform_command = pd.read_excel('data.xlsx', sheet_name='平台命令')
#         data_platform_command = df_platform_command.to_dict(orient='records')
#         db_platform_command = db_radar["platform_command"]
#         db_platform_command.insert_many(data_platform_command)
#         print("数据已成功导入到 MongoDB!")
#         return'successfully!'



#     else:
#         print("雷达上线,ID：",ID_str)
#         db_base_data_radar.update_one({"ID": ID_str}, {'$set': {'is_online': 1}})  
        

# #雷达离线
# def new_radar_disconn(sock):
#     global radar_list
#     for temp_radar in radar_list:
#         if temp_radar.Socket == sock:
#             ID_str=temp_radar.ID_str
#             db_base_data_radar.update_one({"ID": ID_str}, {'$set': {'is_online': 0}})  
#             print("雷达下线,ID：",ID_str)
#             radar_list.remove(temp_radar)
#             break
            



#     radar_list.append(Radar(ID_str=ID_str, Socket=sock))
#     base_radar_data = db_base_data_radar.find_one({"ID": ID_str})
#     if base_radar_data is None:
#         print("新雷达连接,ID：",ID_str)
#         db_base_data_radar.insert_one({
#             "ID": ID_str,
#             "is_online": 1,
#             "is_work":0,
#             })
#     else:
#         print("雷达上线,ID：",ID_str)
#         db_base_data_radar.update_one({"ID": ID_str}, {'$set': {'is_online': 1}})  

# #接收图像数据
# def upload_image_data_form_radar(ID_str, receive_data_byte):
#     seq_int = struct.unpack('<I', receive_data_byte[0:4])[0]
#     md5_str = receive_data_byte[4:20].hex()
#     #MD5校验
#     if hashlib.md5(receive_data_byte[20:]).hexdigest() == md5_str:
#         time_stamp=struct.unpack('<I', receive_data_byte[20:24])[0]
#         mission_ID=struct.unpack('<I', receive_data_byte[24:28])[0]
#         rng_res=struct.unpack('<f', receive_data_byte[28:32])[0]
#         rng_num=struct.unpack('<I', receive_data_byte[32:36])[0]
#         rng_min=struct.unpack('<f', receive_data_byte[36:40])[0]
#         ang_res=struct.unpack('<f', receive_data_byte[40:44])[0]
#         ang_num=struct.unpack('<I', receive_data_byte[44:48])[0]
#         ang_min=struct.unpack('<f', receive_data_byte[48:52])[0]
#         data_type=struct.unpack('<I', receive_data_byte[52:56])[0]
#         sca_im_size=struct.unpack('<I', receive_data_byte[56:60])[0]
#         img_max_amp=struct.unpack('<f', receive_data_byte[60:64])[0]
#         M=rng_num
#         N=ang_num
#         img_data_byte=snappy.uncompress(receive_data_byte[64:])
#         #定位数据库和文件地址
#         db_this_radar=client[ID_str]
#         db_img_data=db_this_radar["img_data_"+str(mission_ID)]
#         file_name_magnitude="magnitude_" + str(seq_int) + ".tiff"
#         file_name_phase = "phase_" + str(seq_int) + ".tiff"
#         file_name_xy = "xy_" + str(seq_int) + ".png"
#         file_road=base_file_path+ID_str+"/work_data/"+str(mission_ID)+"/image_data/"
#         #按照float16解 前两字节为幅度，后面为相位
#         img_magnitude_float32 = np.frombuffer(img_data_byte[:2*M*N], dtype=np.float16).reshape(N, M).astype(np.float32)
#         img_phase_float32 = np.frombuffer(img_data_byte[2 * M * N:], dtype=np.float16).reshape(N, M).astype(np.float32)
#         # 保存为tiff格式
#         imwrite(file_road+file_name_magnitude, img_magnitude_float32)
#         imwrite(file_road+file_name_phase, img_phase_float32)
#         img_doc = {
#             '扫描序号': seq_int,
#             '时间戳': time_stamp,
#             '任务ID': mission_ID,
#             '距离像素间隔': rng_res,
#             '距离像素点数量': rng_num,
#             '最小距离': rng_min,
#             '角度像素间隔': ang_res,
#             '角度像素点数量': ang_num,
#             '最小角度': ang_min,
#             '数据类型': data_type,
#             '最大幅值': img_max_amp,
#             'road_phase': file_road+file_name_phase,
#             'road_magnitude': file_road+file_name_magnitude,
#             'road_xy': file_road+file_name_xy,
#         }
#         params = {
#             "img_road": file_road+file_name_magnitude,
#             "rng_min": rng_min,
#             "rng_res": rng_res,
#             "ang_min": ang_min,
#             "ang_res": ang_res,
#             "save_road": file_road+file_name_xy,
#             "M": M,
#             "N": N,
#         }
#         process = multiprocessing.Process(target=polar2cart, args=(params,))
#         process.start()
#         # polar2cart_list.append(params)

#         db_img_data.insert_one(img_doc)
#         print("接收到雷达图像数据,ID:",ID_str)
#         print("图像大小:",sca_im_size)
#     else:
#         print("图像数据MD5校验不通过,ID:",ID_str)
#     return ""


# def upload_deformation_or_confidence_data_form_radar(ID_str, receive_data_byte):
#     seq_int = struct.unpack('<I', receive_data_byte[0:4])[0]
#     md5_str = receive_data_byte[4:20].hex()
#     #MD5校验
#     if hashlib.md5(receive_data_byte[20:]).hexdigest() == md5_str:
#         time_stamp=struct.unpack('<I', receive_data_byte[20:24])[0]
#         mission_ID=struct.unpack('<I', receive_data_byte[24:28])[0]
#         rng_res=struct.unpack('<f', receive_data_byte[28:32])[0]
#         rng_num=struct.unpack('<I', receive_data_byte[32:36])[0]
#         rng_min=struct.unpack('<f', receive_data_byte[36:40])[0]
#         ang_res=struct.unpack('<f', receive_data_byte[40:44])[0]
#         ang_num=struct.unpack('<I', receive_data_byte[44:48])[0]
#         ang_min=struct.unpack('<f', receive_data_byte[48:52])[0]
#         data_type=struct.unpack('<I', receive_data_byte[52:56])[0]
#         defo_im_size=struct.unpack('<I', receive_data_byte[56:60])[0]
#         M=rng_num
#         N=ang_num
#         data_byte=snappy.uncompress(receive_data_byte[60:])
#         data_float16 = np.frombuffer(data_byte, dtype=np.float16).reshape(N, M)
#         #定位数据库和文件地址
#         db_this_radar=client[ID_str]
#         if data_type == 20:
#             file_road=base_file_path+ID_str+"/work_data/"+str(mission_ID)+"/deformation_data/"
#             file_name = "deformation_img_" + str(seq_int) + ".tiff"
#             db_deformation_data=db_this_radar["deformation_data_"+str(mission_ID)]
#         elif data_type == 30:
#             file_road=base_file_path+ID_str+"/work_data/"+str(mission_ID)+"/Confidence_data/"
#             file_name = "Confidence_img_" + str(seq_int) + ".tiff"
#             db_confidence_data=db_this_radar["confidence_data_"+str(mission_ID)]
#         else:
#             return ""
#         imwrite(file_road+file_name, data_float16)
#         img_doc = {
#             '扫描序号': seq_int,
#             '时间戳': time_stamp,
#             '任务ID': mission_ID,
#             '距离像素间隔': rng_res,
#             '距离像素点数量': rng_num,
#             '最小距离': rng_min,
#             '角度像素间隔': ang_res,
#             '角度像素点数量': ang_num,
#             '最小角度': ang_min,
#             '数据类型': data_type,
#             '图像大小': defo_im_size,
#             'road': file_road+file_name
#         }
#         if data_type == 20:
#             db_deformation_data.insert_one(img_doc)
#             print("接收到形变图像数据,ID:",ID_str)
#         elif data_type == 30:
#             db_confidence_data.insert_one(img_doc)
#             print("接收到置信度数据,ID:",ID_str)
#     else:
#         print("形变图像数据/置信度数据MD5校验不通过,ID:",ID_str)
#     return ""


# def upload_move_target_data_form_radar(ID_str, receive_data_byte):
#     seq_int = struct.unpack('<I', receive_data_byte[0:4])[0]
#     md5_str = receive_data_byte[4:20].hex()
#     #MD5校验
#     if hashlib.md5(receive_data_byte[20:]).hexdigest() == md5_str:
#         time_stamp=struct.unpack('<I', receive_data_byte[20:24])[0]
#         mission_ID=struct.unpack('<I', receive_data_byte[24:28])[0]
#         rng_res=struct.unpack('<f', receive_data_byte[28:32])[0]
#         rng_num=struct.unpack('<I', receive_data_byte[32:36])[0]
#         rng_min=struct.unpack('<f', receive_data_byte[36:40])[0]
#         ang_res=struct.unpack('<f', receive_data_byte[40:44])[0]
#         ang_num=struct.unpack('<I', receive_data_byte[44:48])[0]
#         ang_min=struct.unpack('<f', receive_data_byte[48:52])[0]
#         data_type=struct.unpack('<I', receive_data_byte[52:56])[0]
#         move_target_num=struct.unpack('<I', receive_data_byte[56:60])[0]
#         move_target_data_size=struct.unpack('<I', receive_data_byte[60:64])[0]
#         M=rng_num
#         N=ang_num
#         move_target_data_byte=receive_data_byte[64:]
#         img_doc = {
#             '扫描序号': seq_int,
#             '时间戳': time_stamp,
#             '任务ID': mission_ID,
#             '距离像素间隔': rng_res,
#             '距离像素点数量': rng_num,
#             '最小距离': rng_min,
#             '角度像素间隔': ang_res,
#             '角度像素点数量': ang_num,
#             '最小角度': ang_min,
#             '数据类型': data_type,
#             '动目标个数': move_target_num,
#         }
#         all_target=[]
#         for i in range(move_target_num):
#             each_target={
#                 "目标编号":struct.unpack('<f', move_target_data_byte[0+16*i:4+16*i])[0],
#                 "目标角度":struct.unpack('<f', move_target_data_byte[4+16*i:8+16*i])[0],
#                 "目标距离":struct.unpack('<f', move_target_data_byte[8+16*i:12+16*i])[0],
#                 "目标速度":struct.unpack('<f', move_target_data_byte[12+16*i:16+16*i])[0]
#                 }
#             all_target.append(each_target)
#         img_doc['目标信息']=all_target
#         #定位数据库
#         db_this_radar=client[ID_str]
#         db_move_target_data=db_this_radar["move_target_data_"+str(mission_ID)]
#         db_move_target_data.insert_one(img_doc)
#         print("接收到移动目标数据,ID:",ID_str)
#     else:
#         print("移动目标数据MD5校验不通过,ID:",ID_str)
#     return ""


# def add_log_form_radar(ID_str, receive_data_byte):
#     name_str = receive_data_byte[:128].decode('ascii').split(',')[0]
#     log_road = base_file_path+ID_str + "/log_file/"
#     print(log_road+name_str)
#     with open(log_road+name_str, 'ab') as f:
#         f.write(receive_data_byte[128:])
#         print("接收log文件成功")
#     return ""


# def update_scene_parameter(ID_str, receive_data_byte):
#     db_this_radar=client[ID_str]
#     db_scene_parameter=db_this_radar["scene_parameter"]
#     for scene_parameter_doc in db_scene_parameter.find():
#         start_byte_position=scene_parameter_doc.get('start_byte_position')
#         all_byte_int=scene_parameter_doc.get('all_byte')
#         data_type=scene_parameter_doc.get('data_type')
#         temp_data=''
#         if data_type == 'int16':
#             temp_data = struct.unpack('<h', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#         elif data_type == 'int32':
#             temp_data = struct.unpack('<I', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#         elif data_type == 'float32': 
#             temp_data = struct.unpack('<f', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#         elif data_type == 'float64': 
#             temp_data = struct.unpack('<d', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#         db_scene_parameter.update_one({'start_byte_position': start_byte_position}, {'$set': {'data': temp_data}})
#     print("设备场景参数写入完成,ID:",ID_str)


# def update_radar_imformation(ID_str, receive_data_byte):
#     db_this_radar=client[ID_str]
#     db_radar_information=db_this_radar["radar_information"]
#     for radar_imformation_doc in db_radar_information.find():
#         start_byte_position=radar_imformation_doc.get('start_byte_position')
#         all_byte_int=radar_imformation_doc.get('all_byte')
#         data_type=radar_imformation_doc.get('data_type')
#         temp_data=''
#         if data_type == 'charchar':
#             temp_data=temp_data+'v'
#             for i in range(all_byte_int):
#                 one_data=str(int(receive_data_byte[start_byte_position+i:start_byte_position+i+1].hex()))
#                 temp_data=temp_data+one_data+"."
#             temp_data=temp_data[:-1]
#         elif data_type == 'char':
#             temp_data=str(int(receive_data_byte[start_byte_position:start_byte_position+all_byte_int][::-1].hex()))
#         elif data_type == 'float32': 
#             temp_data = struct.unpack('<f', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#         elif data_type == 'float64': 
#             temp_data = struct.unpack('<d', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#         elif data_type == 'int16': 
#             temp_data = struct.unpack('<h', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
#         elif data_type == 'int32':
#             temp_data = struct.unpack('<I', receive_data_byte[start_byte_position:start_byte_position+all_byte_int])[0]
        
#         db_radar_information.update_one({'start_byte_position': start_byte_position}, {'$set': {'data': temp_data}})
#     print("雷达信息写入完成,ID：",ID_str)
#     return ""


# #更新雷达时间戳
# def update_radar_time(ID_str, data):
#     data_len_str="04"+"00"*3
#     # timestamp_int = int(time.time())
#     timestamp_int = int(time.mktime(time.localtime()))      #获取系统时间
#     send_data_str = struct.pack('<I', timestamp_int).hex()
#     print("时间戳同步完成:",timestamp_int)
#     return send_data_str


# def polar2cart_detect():
#     global polar2cart_list
#     while not stop_server:
#         if(len(polar2cart_list)):
#             polar2cart(polar2cart_list.pop(0))


# def polar2cart(params):
#     print("开始坐标系转换")
#     img = imread(params["img_road"])
#     img = np.rot90(img.astype(np.float32))

#     # 已知参数
#     r_min = params["rng_min"]  # 最近距离 10m
#     r_res = params["rng_res"]  # 每个像素代表 0.3m
#     theta_min = params["ang_min"]  # 起始角度
#     theta_res = params["ang_res"]
#     M=params["M"]
#     N=params["N"]
#     theta_values = np.linspace(theta_min, theta_min + (N - 1) * theta_res, N)  # 角度范围
#     r_values = np.linspace(r_min + (M - 1) * r_res, r_min, M)  # 半径范围

#     # 生成极坐标网格
#     Theta, R = np.meshgrid(theta_values, r_values)

#     # 计算对应的笛卡尔坐标
#     X = R * np.cos(Theta)
#     Y = R * np.sin(Theta)

#     # 生成笛卡尔网格 (新的规则网格)
#     x_cart = np.linspace(X.min(), X.max(), N)
#     y_cart = np.linspace(Y.min(), Y.max(), M)
#     X_cart, Y_cart = np.meshgrid(x_cart, y_cart)

#     # 插值到笛卡尔坐标
#     cartesian_img = griddata((X.flatten(), Y.flatten()), img.flatten(),(X_cart, Y_cart), method='linear', fill_value=0)
#     img = 20 * np.log10(np.where(cartesian_img == 0, np.min(cartesian_img[cartesian_img != 0]), cartesian_img)/np.max(cartesian_img))
#     img = np.rot90(img,2)
#     plt.axis('off')
#     # plt.colorbar()
#     #找到图片最小值
#     min_val = np.min(img)
#     alpha = np.ones_like(img)
#     alpha[img == min_val] = 0  # 使最低值区域透明
#     plt.imshow(img, cmap='jet',  aspect='auto',alpha=alpha)
#     plt.savefig(params["save_road"], bbox_inches='tight',pad_inches=0)


# def temp():
#     print("开始坐标系转换")
#     img = imread("001005ad/work_data/1737360812/image_data/magnitude_132.tiff")
#     img = np.rot90(img.astype(np.float32))

#     # 已知参数
#     r_min = 100  # 最近距离 10m
#     r_res = 0.3  # 每个像素代表 0.3m
#     theta_min = 0.87266222+2  # 起始角度
#     theta_res = 0.0019212
#     M=668
#     N=908
#     theta_values = np.linspace(theta_min, theta_min + (N - 1) * theta_res, N)  # 角度范围
#     r_values = np.linspace(r_min + (M - 1) * r_res, r_min, M)  # 半径范围

#     # 生成极坐标网格
#     Theta, R = np.meshgrid(theta_values, r_values)

#     # 计算对应的笛卡尔坐标
#     X = R * np.cos(Theta)
#     Y = R * np.sin(Theta)
#     # print(X.min(),",",X.max())
#     # print(Y.min(),",",Y.max())
#     # 生成笛卡尔网格 (新的规则网格)
#     x_cart = np.linspace(X.min(), X.max(), N)
#     y_cart = np.linspace(Y.min(), Y.max(), M)
#     X_cart, Y_cart = np.meshgrid(x_cart, y_cart)

#     # 插值到笛卡尔坐标
#     cartesian_img = griddata((X.flatten(), Y.flatten()), img.flatten(),(X_cart, Y_cart), method='linear', fill_value=0)
#     img = 20 * np.log10(np.where(cartesian_img == 0, np.min(cartesian_img[cartesian_img != 0]), cartesian_img)/np.max(cartesian_img))
#     img = np.rot90(img,2)

#     # img = rotate(img, 50, reshape=True)  # 图片小度数精确旋转5度

#     plt.axis('off')
#     # plt.colorbar()
#     #找到图片最小值
#     min_val = np.min(img)
#     alpha = np.ones_like(img)
#     alpha[img == min_val] = 0  # 使最低值区域透明
#     plt.imshow(img, cmap='jet',  aspect='auto',alpha=alpha)
#     plt.savefig("001005ad/work_data/1737360812/image_data/xy_133.png", bbox_inches='tight',pad_inches=0, transparent=True)
#     plt.savefig("001005ad/work_data/1737360812/image_data/xy_134.png",pad_inches=0, transparent=True)