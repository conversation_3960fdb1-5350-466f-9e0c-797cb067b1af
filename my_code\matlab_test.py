import matlab.engine
import os

eng = None
matlab_code_path = "code/matlab_code"



def matlab_begin():
    global eng
    eng = matlab.engine.start_matlab()
    #添加文件夹路径
    matlab_path = os.path.abspath(matlab_code_path)
    eng.addpath(matlab_path, nargout=0)


def matlab_end():
    global eng
    if eng is not None:
        eng.quit()
        eng = None


def matlab_test():
    t = eng.ImgProcess.add(4,2)
    print(t)
    t = eng.ImgProcess.sub(4,4)
    print(t)
    t = eng.ImgProcess.test(matlab.double([8,16]))
    print(t[0])

def matlab_Img2DEM():
    ImgFlie="E:\实验室\云平台\大王山\深圳\magnitude_132.tiff"
    DEMFile = "E:\实验室\云平台\大王山\深圳\ALPSMLC30_N022E113_DSM.tif"
    output_path = "E:/实验室/云平台/temp_flake/code/web_code/radar_image.png"
    PolarPara=matlab.double([0.87266222,0.0019212,100,0.3,0.5236])
    radarPara=matlab.double([113.95612,22.80338,80])
    Resample_flag = 1
    ImgRange = eng.ImgProcess.Img2DEM(PolarPara,radarPara,ImgFlie,DEMFile,output_path,Resample_flag)
    print(ImgRange[0])