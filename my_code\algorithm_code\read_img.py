from pymongo import MongoClient
from PIL import Image
from tifffile import imwrite, imread        #tiff文件写入
import numpy as np
from datetime import datetime, timezone               #时间戳转换

client = MongoClient('mongodb://localhost:27017/')
db_base_data=client['base_data']
db_base_data_radar=db_base_data['radar']
db_base_data_scene=db_base_data['scene']


#监测区域坐标转像素坐标
#输入：雷达ID，任务ID，监测区域标签，经纬度坐标
#geoCoord = [左下角经度，左下角纬度，右上角经度，右上角纬度]
def geoToPixel(radar_ID,mission_ID,area_lable,geoCoord):
    #获取图像覆盖坐标
    db_radar_base=db_base_data_radar.find_one({"ID": radar_ID})
    mission_list = db_radar_base["mission_ID"]
    this_mission = next((item for item in mission_list if item["ID"] == mission_ID), None)
    Img_range = this_mission["Img_range"]
    #获取图像路径
    db_radar_detail = client[radar_ID]
    db_img = db_radar_detail["img_data_"+mission_ID]
    img_detail = db_img.find_one({"任务ID": int(mission_ID)}, sort=[("时间戳", -1)])
    img_path = img_detail["road_xy"]
    #读取图像像素大小
    img = Image.open(img_path)
    width, height = img.size
    #计算单位像素经纬度
    pixel_width = (Img_range[2] - Img_range[0]) / width
    pixel_height = (Img_range[3] - Img_range[1]) / height
    #计算像素坐标(四个点从左下角开始，顺时针旋转)
    are_Coord = []
    are_Coord.append([int((geoCoord[0] - Img_range[0]) / pixel_width),int((geoCoord[1] - Img_range[1]) / pixel_height)])
    are_Coord.append([int((geoCoord[0] - Img_range[0]) / pixel_width),int((geoCoord[3] - Img_range[1]) / pixel_height)])
    are_Coord.append([int((geoCoord[2] - Img_range[0]) / pixel_width),int((geoCoord[3] - Img_range[1]) / pixel_height)])
    are_Coord.append([int((geoCoord[2] - Img_range[0]) / pixel_width),int((geoCoord[1] - Img_range[1]) / pixel_height)])
    #写入像素坐标
    db_monitor = db_radar_detail["monitor_area_"+mission_ID]
    result = db_monitor.update_one({"标签": "123"}, {"$set": {"区域像素坐标": are_Coord}})


#接收图像后的形变处理算法
#输入：雷达ID，任务ID
def img_process(radar_ID,mission_ID):
    #获取文件路径
    db_this_radar = client[radar_ID]
    db_img_data = db_this_radar["img_data_"+mission_ID]
    doc_lastest_img = db_img_data.find_one(sort=[("时间戳", -1)])
    #输入matlab算法，处理完成，返回文件地址
    ##########################################################
    img_deformation_path = ""
    #读取图像文件
    db_img_data.update_one({"扫描序号":doc_lastest_img["扫描序号"]},{"$set":{"road_dedormation":img_deformation_path}})
    img = imread(img_deformation_path)
    img = np.rot90(img.astype(np.float32))
    height, width = img.shape[:2]
    #获取图像覆盖坐标
    db_radar_base=db_base_data_radar.find_one({"ID": radar_ID})
    mission_list = db_radar_base["mission_ID"]
    this_mission = next((item for item in mission_list if item["ID"] == mission_ID), None)
    Img_range = this_mission["Img_range"]
    pixel_width = (Img_range[2] - Img_range[0]) / width
    pixel_height = (Img_range[3] - Img_range[1]) / height
    #遍历所有监测区域，获取平均形变量
    doc_monitor = db_this_radar["monitor_area_"+mission_ID].find()
    #形变区域储存数据库
    for each_monitor in doc_monitor:
        #获取监测区域像素坐标
        are_Coord = []
        are_Coord.append(each_monitor["westLon"])
        are_Coord.append(each_monitor["southLat"])
        are_Coord.append(each_monitor["eastLon"])
        are_Coord.append(each_monitor["northLat"])
        #获取监测区域平均形变
        row_start = int((are_Coord[0] - Img_range[0]) / pixel_width)
        row_end = int((are_Coord[2] - Img_range[0]) / pixel_width)
        col_start = int((are_Coord[1] - Img_range[1]) / pixel_height)
        col_end = int((are_Coord[3] - Img_range[1]) / pixel_height)
        mean_deformation = np.mean(img[col_start:col_end, row_start:row_end])
        #写入数据库
        if not "deformation_data_"+mission_ID in db_this_radar.list_collection_names():
            db_this_radar.create_collection(
                "deformation_data_"+mission_ID,
                timeseries={
                    "timeField": "timestamp",
                    "metaField": "area_ID",
                    "granularity": "minutes"
                }
            )
        #插入时间序列
        coll_deformation = db_this_radar["deformation_data_"+mission_ID]
        timestamp = datetime.fromtimestamp(float(doc_lastest_img["时间戳"]), tz=timezone.utc)
        coll_deformation.insert_one({"timestamp": timestamp, "deformation": float(mean_deformation),"area_ID": str(each_monitor["_id"])})

