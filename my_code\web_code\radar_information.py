from flask import Blueprint, request, jsonify
from flask_jwt_extended import  jwt_required
from pymongo import MongoClient
import my_code.radar_code as radar_code

radar_information = Blueprint("radar_information", __name__)
client = MongoClient('mongodb://localhost:27017/')
db_base_data=client['base_data']
db_base_data_radar=db_base_data['radar']

#查询雷达信息
@radar_information.route('/update_radar_information', methods=['POST'])
@jwt_required()
def web_update_radar_information():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        print(data)
        radar_ID=data["radar_ID"]
        # result = radar_code.ArcSAR_id_map[radar_ID].query_radar_state()
        if radar_ID in radar_code.ArcSAR_id_map:
            result = radar_code.ArcSAR_id_map[radar_ID].query_radar_state()
        else:
            result = "雷达离线"

        # result = radar_code.query_radar_state(radar_ID)
        db_this_radar=client[radar_ID]
        db_radar_information=db_this_radar["radar_information"]
        results_doc = db_radar_information.find({"serial_number":{"$gte":1,"$lte":49}})
        radar_information_data=[]
        for each_doc in results_doc:
            each_doc_data={
                "ID":each_doc['serial_number'],
                "label":each_doc['mean'],
                "value":each_doc['data']
                }
            radar_information_data.append(each_doc_data)
        if result=="success":
            return jsonify({
                "states": "success",  # 状态信息
                "message": "查询成功",  # 附加消息
                "data": radar_information_data  # 数据
            })
        else:
            return jsonify({
                "states": "warning",  # 状态信息
                "message": "查询失败，该信息为雷达最后在线时刻的状态",  # 附加消息
                "data": radar_information_data  # 数据
            })

    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500

