from flask import Blueprint, request, jsonify
from flask_jwt_extended import  jwt_required
from pymongo import MongoClient
import shutil                               # 用于删除文件夹
import os
from bson import ObjectId



radar_manage = Blueprint("radar_manage", __name__)
client = MongoClient('mongodb://localhost:27017/')
db_base_data=client['base_data']
db_base_data_radar=db_base_data['radar']
db_base_data_scene=db_base_data['scene']

#雷达数据路径
base_file_path=""


#所有雷达管理列表获取
@radar_manage.route('/getAllRadar', methods=['POST'])
@jwt_required()
def web_radar_manage_getAllRadar():
    try:
        # data = request.get_json()  # 判断用户权限
        scene_list=db_base_data_scene.find({}, {})
        send_data=[]
        for each_scene_doc in scene_list:
            radar_list_doc=each_scene_doc["radar_ID"]
            radar_list=[]
            each_scene={}
            for radarID_each in radar_list_doc:
                radar_each = db_base_data_radar.find_one({"ID":radarID_each}, {"_id": 0})
                radar_list.append(radar_each)
            each_scene["name"]=each_scene_doc["name"]
            each_scene["ID"]=str(each_scene_doc["_id"])
            each_scene["radar"]=radar_list
            send_data.append(each_scene)
        return jsonify({"status": "success", "message": "雷达管理列表加载成功", "data": send_data})

            




        radar_list_doc=db_base_data_radar.find({}, {"_id": 0})  # 排除 "_id"
        radar_list=[]
        for each_radar_doc in radar_list_doc:
            radar_list.append(each_radar_doc)
            # print(each_radar_doc)
        return jsonify({"status": "success", "message": "雷达管理列表加载成功", "data": radar_list})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


#雷达重命名
@radar_manage.route('/change_radar_name', methods=['POST'])
# @jwt_required()
def web_change_radar_name():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        new_name=data['name']
        # radar_ID="001005ad"
        # new_name="雷达1"
        db_base_data_radar.update_one({"ID": radar_ID}, {"$set": {"name": new_name}})
        return jsonify({"status": "success", "message": "雷达重命名成功"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


#雷达任务重命名
@radar_manage.route('/change_mission_name', methods=['POST'])
@jwt_required()
def web_change_mission_name():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        new_name=data['name']
        if db_base_data_radar.find_one({"ID": radar_ID, "mission_ID.ID": mission_ID}):
            db_base_data_radar.update_one(
                {"ID": radar_ID, "mission_ID.ID": mission_ID},  # 找到包含该任务 ID 的文档
                {"$set": {"mission_ID.$.name": new_name}}  # 更新该任务的 name 字段
            )
            return jsonify({"status": "success", "message": "任务名称修改成功"})
        else:
            return jsonify({"status": "error", "message": "修改失败,数据库中无法查找到对应数据"})

    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500

#场景重命名
@radar_manage.route('/change_scene_name', methods=['POST'])
# @jwt_required()
def web_change_scene_name():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        scene_ID=data['sceneID']
        new_name=data['name']
        # radar_ID="001005ad"
        # new_name="雷达1"
        db_base_data_scene.update_one({"_id": ObjectId(scene_ID)}, {"$set": {"name": new_name}})
        return jsonify({"status": "success", "message": "场景重命名成功"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


#删除雷达任务
@radar_manage.route('delete_mission', methods=['POST'])
@jwt_required()
def web_delete_mission():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        mission_ID=data['mission_ID']
        db_base_data_radar.update_one(
            {"ID": radar_ID},  # 找到包含该任务 ID 的文档
            {"$pull": {"mission_ID": {"ID": mission_ID}}}  # 更新该任务的 name 字段
        )
        #删除数据库相关路径
        db_this_radar=client[radar_ID]
        db_this_radar["img_data_"+mission_ID].drop()
        db_this_radar["confidence_data_"+mission_ID].drop()
        db_this_radar["deformation_data_"+mission_ID].drop()
        db_this_radar["monitor_area_"+mission_ID].drop()
        #删除相关文件
        file_path = base_file_path + radar_ID + "/work_data/" +  mission_ID # 任务文件夹路径
        if os.path.exists(file_path):
            shutil.rmtree(file_path)
        print("删除雷达："+radar_ID+" 的任务ID："+mission_ID)
        return jsonify({"status": "success", "message": "删除成功"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500
    

#删除雷达设备
@radar_manage.route('/delete_radar', methods=['POST'])
@jwt_required()
def web_delete_radar():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        print(data)
        radar_ID=data['radar_ID']
        #删除数据库中雷达信息
        print("4")
        client.drop_database(radar_ID)
        print("3")
        db_this_radar=db_base_data_radar.find_one({"ID": radar_ID})
        print("5")
        scene_ID = db_this_radar["scene"]
        #删除场景数据库中绑定的雷达信息
        print("1")
        db_base_data_scene.update_one({"_id": ObjectId(scene_ID)},{"$pull": {"radar_ID": radar_ID}})
        print("2")
        #删除雷达数据库中对应信息
        db_base_data_radar.delete_one({"ID": radar_ID})
        #删除用户绑定的雷达！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！未实现
        #删除相关文件
        file_path = base_file_path + radar_ID # 任务文件夹路径
        if os.path.exists(file_path):
            shutil.rmtree(file_path)
        print("删除雷达：",radar_ID)
        return jsonify({"status": "success", "message": "删除雷达成功"})
    
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500


#场景坐标设置
@radar_manage.route('/change_scene_coordinates', methods=['POST'])
@jwt_required()
def web_change_scene_coordinates():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        scene_ID=data['scene_ID']
        coordinates=data['coordinates']
        coordinates[2]=1500
        db_base_data_radar.update_one(
            {"_id": ObjectId(scene_ID)},  # 找到包含该任务 ID 的文档
            {"$set": {"coordinates": coordinates}}  # 更新该任务的 name 字段
        )
        return jsonify({"status": "success", "message": "场景坐标修改成功"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500
    

#雷达坐标设置
@radar_manage.route('/change_radar_coordinates', methods=['POST'])
@jwt_required()
def web_change_radar_coordinates():
    try:
        data = request.get_json()  # 获取前端提交的 JSON 数据
        radar_ID=data['radar_ID']
        coordinates=data['coordinates']
        db_base_data_radar.update_one(
            {"ID": radar_ID},  # 找到包含该任务 ID 的文档
            {"$set": {"radar_coordinates": coordinates}}  # 更新该任务的 name 字段
        )
        return jsonify({"status": "success", "message": "雷达坐标修改成功"})
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500