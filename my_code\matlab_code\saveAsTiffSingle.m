function t = saveAsTiffSingle(X, filepath, metadata)
% SAVEASITIFFSINGLE 保存单精度浮点数据为 TIFF 文件，并将 metadata 写入 ImageDescription
%
% 输入:
%   X        - 单精度浮点数据矩阵
%   filepath - 保存路径（如 'image.tif'）
%   metadata - 可选参数，默认为空 []
%
% 输出:
%   t        - Tiff 对象（已关闭）

    % 检查 metadata 是否提供，未提供则设为默认值 []
    if nargin < 3 || isempty(metadata)
        metadata = []; % 默认值
    end

    % 转换数据为单精度
    im = single(X);
    
    % 创建 Tiff 对象
    t = Tiff(filepath, 'w');
    
    % 设置 TIFF 标签
    tagstruct.ImageLength = size(im, 1); 
    tagstruct.ImageWidth  = size(im, 2);  
    tagstruct.Photometric = Tiff.Photometric.MinIsBlack; % 1 表示灰度图
    tagstruct.BitsPerSample = 32;       % single 类型为 32 位
    tagstruct.SamplesPerPixel = 1;      % 单通道（灰度）
    tagstruct.PlanarConfiguration = Tiff.PlanarConfiguration.Chunky;
    tagstruct.Software = 'MATLAB';
    tagstruct.SampleFormat = Tiff.SampleFormat.IEEEFP; % 3 表示浮点数
    
    % 将 metadata 转换为 JSON 字符串并写入 ImageDescription
    if ~isempty(metadata)
        % 将结构体转为 JSON 字符串
        jsonStr = jsonencode(metadata);
        % 写入 ImageDescription 标签
        tagstruct.ImageDescription = jsonStr;
    end
    
    % 写入标签和数据
    t.setTag(tagstruct);
    t.write(im);
    t.close();
end