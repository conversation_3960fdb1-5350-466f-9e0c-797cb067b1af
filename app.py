# 标准库导入
import json
import os
from datetime import timedelta

# 第三方库导入
from pymongo import MongoClient
from bson import ObjectId
from flask import Flask, request, send_from_directory, jsonify, send_file
from flask_pymongo import PyMongo
from flask_cors import CORS
from flask_jwt_extended import jwt_required, get_jwt_identity, JWTManager

# 本地模块导入
from my_code.web_code.user import user
from my_code.web_code.radar_manage import radar_manage
from my_code.web_code.data_analysis import data_analysis
from my_code.web_code.radar_information import radar_information
from my_code.web_code.scene_parameter import scene_parameter

# 初始化Flask应用
app = Flask(__name__)

# 基础配置
app.secret_key = 'your_secret_key'  # TODO: 生产环境需要更改为安全的密钥

# CORS配置
CORS(app)  # 允许跨域访问

# MongoDB配置
app.config["MONGO_URI"] = "mongodb://localhost:27017/web_user"
mongo = PyMongo(app)  # 用于用户认证的MongoDB实例

# 主数据库连接
client = MongoClient('mongodb://localhost:27017/')
db_base_data = client['base_data']
db_base_data_radar = db_base_data['radar']      # 雷达数据集合
db_base_data_user = db_base_data['users']       # 用户数据集合
db_base_data_scene = db_base_data['scene']      # 场景数据集合

# JWT配置
app.config['JWT_SECRET_KEY'] = 'super-secret'  # TODO: 生产环境需要更改为安全的密钥
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(days=1)    # Access Token有效期：1天
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)  # Refresh Token有效期：30天
jwt = JWTManager(app)

# 注册蓝图
app.register_blueprint(user, url_prefix="/user")                           # 用户管理模块
app.register_blueprint(radar_manage, url_prefix="/radar_manage")           # 雷达管理模块
app.register_blueprint(data_analysis, url_prefix="/data_analysis")        # 数据分析模块
app.register_blueprint(radar_information, url_prefix="/radar_information") # 雷达信息模块
app.register_blueprint(scene_parameter, url_prefix="/scene_parameter")     # 场景参数模块


# API路由定义
# -------------

# 雷达相关路由
@app.route('/check_all_radar', methods=['GET', 'POST'])
@jwt_required()
def web_check_all_radar():
    """获取指定场景下的所有雷达信息"""
    data = request.get_json()
    scene_ID = data["scene_ID"]
    db_scene = db_base_data_scene.find_one({"_id": ObjectId(scene_ID)})
    radar_ID = db_scene["radar_ID"]
    radar_list = []
    for each_ID in radar_ID:
        db_each_radar = db_base_data_radar.find_one({"ID": each_ID})
        radar_list.append({"ID": each_ID, "name": db_each_radar["name"]})
    return jsonify(radar_list)

@app.route('/listen_radar_state', methods=['POST'])
@jwt_required()
def web_listen_radar_state():
    """监听指定雷达的工作状态"""
    try:
        data = request.get_json()
        radar_ID = data['radar_ID']
        radar_state_doc = db_base_data_radar.find_one({"ID": radar_ID})
        radar_state = {
            "is_work": str(radar_state_doc["is_work"]),
            "state": "work" if radar_state_doc["is_work"] == 1 
                    else "online" if radar_state_doc["is_online"] == 1 
                    else "offline"
        }
        return jsonify(radar_state)
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500

# 图像和文件处理路由
@app.route('/<radar_id>/work_data/<mission_id>/image_data/<filename>')
def get_image(radar_id, mission_id, filename):
    """获取指定雷达任务的图像数据"""
    file_path = f'./{radar_id}/work_data/{mission_id}/image_data/{filename}'
    return send_file(file_path, mimetype='image/png')

@app.route('/download/<path:file_path>', methods=['POST', 'GET'])
def web_download(file_path):
    """下载指定路径的文件"""
    normalized_path = os.path.normpath(file_path)
    ALLOWED_BASE_DIR = r"E:/实验室/云平台/react_app/public/source"
    absolute_path = os.path.abspath(os.path.join(ALLOWED_BASE_DIR, normalized_path))
    print("访问文件地址为：", absolute_path)
    
    if not os.path.exists(absolute_path):
        return jsonify({"status": "error", "message": "文件夹不存在"}), 500
    if not os.path.isfile(absolute_path):
        return jsonify({"status": "error", "message": "文件不存在"}), 500
        
    return send_file(
        absolute_path,
        as_attachment=True,
        download_name=os.path.basename(absolute_path)
    )

# 场景相关路由
@app.route('/get_scene_list', methods=['POST'])
@jwt_required()
def web_get_scene_list():
    """获取当前用户可访问的场景列表"""
    try:
        identity = get_jwt_identity()
        identity_dict = json.loads(identity)
        user_name = identity_dict["username"]
        db_user = db_base_data_user.find_one({"username": user_name})
        scene_ID = db_user["scene_ID"]
        
        scene_ID_list = []
        for each_scene in scene_ID:
            result = db_base_data_scene.find_one({"_id": ObjectId(each_scene)})
            scene_ID_list.append({
                "name": result["name"],
                "background_pack": result["background_pack"],
                "ID": each_scene,
                "coordinates": result["coordinates"]
            })
        return jsonify({
            "states": "success",
            "message": "成功获取场景信息",
            "data": scene_ID_list
        })
    except Exception as e:
        print("处理错误:", str(e))
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/image/<path:filename>', methods=['POST', 'GET'])
def get_image1(filename):
    """获取静态图像文件"""
    return send_from_directory('', filename)

if __name__ == '__main__':
    app.run()